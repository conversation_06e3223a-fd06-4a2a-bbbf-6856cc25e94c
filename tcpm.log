[    0.373472] Setting usb_comm capable false
[    0.374607] Setting voltage/current limit 0 mV 0 mA
[    0.374609] polarity 0
[    0.374611] Requesting mux state 0, usb-role 0, orientation 0
[    0.374822] state change INVALID_STATE -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.374830] CC1: 0 -> 0, CC2: 0 -> 0 [state SNK_UNATTACHED, polarity 0, disconnected]
[    0.374833] 2-0022: registered
[    0.374835] Setting usb_comm capable false
[    0.375986] Setting voltage/current limit 0 mV 0 mA
[    0.375993] polarity 0
[    0.375995] Requesting mux state 0, usb-role 0, orientation 0
[    0.376215] cc:=2
[    0.377553] pending state change PORT_RESET -> PORT_RESET_WAIT_OFF @ 100 ms [rev1 NONE_AMS]
[    0.377561] state change PORT_RESET -> PORT_RESET_WAIT_OFF [delayed 100 ms]
[    0.377564] state change PORT_RESET_WAIT_OFF -> SNK_UNATTACHED [rev1 NONE_AMS]
[    0.377567] Start toggling
[    0.380205] VBUS on
[    0.388919] CC1: 0 -> 0, CC2: 0 -> 4 [state TOGGLING, polarity 0, connected]
[    0.388923] state change TOGGLING -> SNK_ATTACH_WAIT [rev1 NONE_AMS]
[    0.388928] pending state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED @ 200 ms [rev1 NONE_AMS]
[    0.588944] state change SNK_ATTACH_WAIT -> SNK_DEBOUNCED [delayed 200 ms]
[    0.588956] state change SNK_DEBOUNCED -> SNK_ATTACHED [rev1 NONE_AMS]
[    0.588959] polarity 1
[    0.588962] Requesting mux state 1, usb-role 2, orientation 2
[    0.589244] state change SNK_ATTACHED -> SNK_STARTUP [rev1 NONE_AMS]
[    0.589262] state change SNK_STARTUP -> SNK_DISCOVERY [rev2 NONE_AMS]
[    0.589266] Setting voltage/current limit 5000 mV 1500 mA
[    0.589276] vbus=0 charge:=1
[    0.589279] state change SNK_DISCOVERY -> SNK_WAIT_CAPABILITIES [rev2 NONE_AMS]
[    0.590479] pending state change SNK_WAIT_CAPABILITIES -> HARD_RESET_SEND @ 620 ms [rev2 NONE_AMS]
[    0.600861] PD RX, header: 0x15a1 [1], 1 objects
[    0.600867] PD RX, object:0x36019096
[    0.600872]  PDO 0: type 0, 5000 mV, 1500 mA [RSHUD]
[    0.601002] state change SNK_WAIT_CAPABILITIES -> SNK_NEGOTIATE_CAPABILITIES [rev2 POWER_NEGOTIATION]
[    0.601009] Setting usb_comm capable true
[    0.601019] cc=2 cc1=0 cc2=4 vbus=0 vconn=sink polarity=1
[    0.601022] Requesting PDO 0: 5000 mV, 1500 mA [mismatch]
[    0.601027] PD TX, header: 0x1042
[    0.604030] PD TX complete, status: 0
[    0.604043] pending state change SNK_NEGOTIATE_CAPABILITIES -> HARD_RESET_SEND @ 60 ms [rev2 POWER_NEGOTIATION]
[    0.605765] PD RX, header: 0x763 [1], 0 objects
[    0.605773] state change SNK_NEGOTIATE_CAPABILITIES -> SNK_TRANSITION_SINK [rev2 POWER_NEGOTIATION]
[    0.605781] pending state change SNK_TRANSITION_SINK -> HARD_RESET_SEND @ 500 ms [rev2 POWER_NEGOTIATION]
[    0.606837] PD RX, header: 0x966 [1], 0 objects
[    0.606840] Setting voltage/current limit 5000 mV 1500 mA
[    0.606853] state change SNK_TRANSITION_SINK -> SNK_READY [rev2 POWER_NEGOTIATION]
[    0.606980] AMS POWER_NEGOTIATION finished
[    0.659400] PD RX, header: 0x1b6f [1], 1 objects
[    0.659404] PD RX, object:0xff008001
[    0.659408] Rx VDM cmd 0xff008001 type 0 cmd 1 len 1 adev           (null)
[    0.659420] tcpm_queue_vdm
[    0.659426] vdm_run_state_machine vdm_state:1
[    0.659428] AMS DISCOVER_IDENTITY start
[    0.659431] vdm_run_state_machine vdm_state:4
[    0.659433] PD TX, header: 0x524f
[    0.663514] PD TX complete, status: 0
[    0.663520] AMS DISCOVER_IDENTITY finished
[    0.663528] vdm_run_state_machine vdm_state:2
[    0.663530] vdm_run_state_machine vdm_state:-1
[    0.665491] PD RX, header: 0x1d6f [1], 1 objects
[    0.665499] PD RX, object:0xff008002
[    0.665503] Rx VDM cmd 0xff008002 type 0 cmd 2 len 1 adev           (null)
[    0.665512] svid 0xff01
[    0.665514] tcpm_queue_vdm
[    0.665517] vdm_run_state_machine vdm_state:1
[    0.665520] AMS DISCOVER_SVIDS start
[    0.665523] vdm_run_state_machine vdm_state:4
[    0.665524] PD TX, header: 0x244f
[    0.668918] PD TX complete, status: 0
[    0.668926] AMS DISCOVER_SVIDS finished
[    0.668933] vdm_run_state_machine vdm_state:2
[    0.668935] vdm_run_state_machine vdm_state:-1
[    0.670886] PD RX, header: 0x1f6f [1], 1 objects
[    0.670894] PD RX, object:0xff018003
[    0.670898] Rx VDM cmd 0xff018003 type 0 cmd 3 len 1 adev           (null)
[    0.670906] SRC SVID 1: 0xff01
[    0.670909] tcpm_queue_vdm
[    0.670912] vdm_run_state_machine vdm_state:1
[    0.670914] AMS DISCOVER_MODES start
[    0.670917] vdm_run_state_machine vdm_state:4
[    0.670919] PD TX, header: 0x264f
[    0.674076] PD TX complete, status: 0
[    0.674086] AMS DISCOVER_MODES finished
[    0.674093] vdm_run_state_machine vdm_state:2
[    0.674095] vdm_run_state_machine vdm_state:-1
[    0.676186] PD RX, header: 0x116f [1], 1 objects
[    0.676189] PD RX, object:0xff018104
[    0.676193] Rx VDM cmd 0xff018104 type 0 cmd 4 len 1 adev ffffffc030ba5808
[    0.676201]  Alternate mode 0: SVID 0xff01, VDO 1: 0x00000405
[    0.676456] tcpm_queue_vdm
[    0.676462] vdm_run_state_machine vdm_state:1
[    0.676465] AMS DFP_TO_UFP_ENTER_MODE start
[    0.676468] vdm_run_state_machine vdm_state:4
[    0.676471] PD TX, header: 0x184f
[    0.679809] PD TX complete, status: 0
[    0.679820] AMS DFP_TO_UFP_ENTER_MODE finished
[    0.679825] vdm_run_state_machine vdm_state:2
[    0.679827] vdm_run_state_machine vdm_state:-1
[    0.681194] PD RX, header: 0x236f [1], 2 objects
[    0.681200] PD RX, object:0xff018110
[    0.681201] PD RX, object:0x1
[    0.681205] Rx VDM cmd 0xff018110 type 0 cmd 16 len 2 adev ffffffc030ba5808
[    0.681240] tcpm_queue_vdm
[    0.681244] vdm_run_state_machine vdm_state:1
[    0.681247] AMS STRUCTURED_VDMS start
[    0.681250] vdm_run_state_machine vdm_state:4
[    0.681255] PD TX, header: 0x2a4f
[    0.685347] PD TX complete, status: 0
[    0.685359] AMS STRUCTURED_VDMS finished
[    0.685368] vdm_run_state_machine vdm_state:2
[    0.685369] vdm_run_state_machine vdm_state:-1
[    0.687436] PD RX, header: 0x256f [1], 2 objects
[    0.687441] PD RX, object:0xff018111
[    0.687444] PD RX, object:0x406
[    0.687447] Rx VDM cmd 0xff018111 type 0 cmd 17 len 2 adev ffffffc030ba5808
[    0.687499] tcpm_queue_vdm
[    0.687554] vdm_run_state_machine vdm_state:1
[    0.687557] AMS STRUCTURED_VDMS start
[    0.687559] vdm_run_state_machine vdm_state:4
[    0.687561] PD TX, header: 0x1c4f
[    0.690593] PD TX complete, status: 0
[    0.690602] AMS STRUCTURED_VDMS finished
[    0.690608] vdm_run_state_machine vdm_state:2
[    0.690609] vdm_run_state_machine vdm_state:-1
[    0.708414] tcpm_queue_vdm
[    0.708449] vdm_run_state_machine vdm_state:1
[    0.708450] AMS ATTENTION start
[    0.708453] vdm_run_state_machine vdm_state:4
[    0.708455] PD TX, header: 0x2e4f
[    0.711716] PD TX complete, status: 0
[    0.711721] AMS ATTENTION finished
[    0.741780] vdm_run_state_machine vdm_state:2
[    0.741799] vdm_run_state_machine vdm_state:-1
