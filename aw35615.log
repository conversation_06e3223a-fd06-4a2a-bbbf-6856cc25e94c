[    0.371274] sw reset
[    0.372178] sw reset
[    0.373468] aw35615 device ID: 0x91
[    0.374601] pd := off
[    0.374603] vbus is already Off
[    0.374604] charge is already Off
[    0.374606] vconn is already Off
[    0.374818] pd header := Sink, Device
[    0.374828] cc1=Open, cc2=Open
[    0.375977] pd := off
[    0.375980] vbus is already Off
[    0.375982] charge is already Off
[    0.375983] vconn is already Off
[    0.376209] pd header := Sink, Device
[    0.376216] cc := Rd
[    0.378706] start drp toggling
[    0.379426] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.379428] IRQ: VBUS_OK, vbus=On
[    0.379435] gpio_intn_value:0
[    0.379545] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.380087] IRQ: 0x80, a: 0x00, b: 0x00, status0: 0x83, status1: 0x28
[    0.380089] IRQ: VBUS_OK, vbus=On
[    0.380091] gpio_intn_value:1
[    0.380200] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.386705] IRQ: 0x00, a: 0x40, b: 0x00, status0: 0x82, status1: 0x28
[    0.386708] IRQ: TOGDONE
[    0.388144] detected cc1=Open, cc2=Rp-1.5
[    0.388147] gpio_intn_value:0
[    0.388256] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.388800] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.388802] gpio_intn_value:1
[    0.388911] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.388916] cc1=Open, cc2=Rp-1.5
[    0.389527] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.389529] IRQ: BC_LVL, handler pending
[    0.389532] gpio_intn_value:0
[    0.389641] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.390183] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.390185] IRQ: BC_LVL, handler pending
[    0.390186] gpio_intn_value:1
[    0.390295] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.391203] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.391205] IRQ: BC_LVL, handler pending
[    0.391206] gpio_intn_value:0
[    0.391317] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.391859] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.391861] IRQ: BC_LVL, handler pending
[    0.391863] gpio_intn_value:1
[    0.391972] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.428494] BC_LVL handler, status0=0x92
[    0.493422] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.493433] IRQ: BC_LVL, handler pending
[    0.493439] gpio_intn_value:0
[    0.493548] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.494089] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.494091] IRQ: BC_LVL, handler pending
[    0.494092] gpio_intn_value:1
[    0.494206] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.495016] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.495019] IRQ: BC_LVL, handler pending
[    0.495020] gpio_intn_value:0
[    0.495130] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.495672] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.495674] IRQ: BC_LVL, handler pending
[    0.495676] gpio_intn_value:1
[    0.495784] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.496744] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.496746] IRQ: BC_LVL, handler pending
[    0.496747] gpio_intn_value:0
[    0.496857] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.497400] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.497403] IRQ: BC_LVL, handler pending
[    0.497404] gpio_intn_value:1
[    0.497512] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.528498] BC_LVL handler, status0=0x92
[    0.589238] pd header := Sink, Device
[    0.589277] vbus is already Off
[    0.590474] pd := on
[    0.598934] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.598941] IRQ: BC_LVL, handler pending
[    0.598947] gpio_intn_value:0
[    0.599057] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.599599] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd0, status1: 0x08
[    0.599601] IRQ: BC_LVL, handler pending
[    0.599603] gpio_intn_value:0
[    0.599712] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.600251] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.600253] IRQ: BC_LVL, handler pending
[    0.600254] IRQ: PD sent good CRC
[    0.600852] PD message header: 15a1 len:4 crc:781bd94a
[    0.600858] gpio_intn_value:1
[    0.601008] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.601676] sending PD message header: 1042
[    0.601680] sending PD message len: 4
[    0.602286] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.602293] IRQ: BC_LVL, handler pending
[    0.602301] gpio_intn_value:0
[    0.602410] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.602950] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.602952] IRQ: BC_LVL, handler pending
[    0.602953] gpio_intn_value:0
[    0.603063] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.603604] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.603606] IRQ: BC_LVL, handler pending
[    0.603607] IRQ: PD tx success
[    0.604026] PD message header: 1a1 len:0 crc:81c2afc1
[    0.604035] gpio_intn_value:0
[    0.604145] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.604683] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.604685] IRQ: BC_LVL, handler pending
[    0.604687] gpio_intn_value:0
[    0.604796] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.605335] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0xd2, status1: 0x08
[    0.605337] IRQ: BC_LVL, handler pending
[    0.605338] IRQ: PD sent good CRC
[    0.605757] PD message header: 763 len:0 crc:916dbf38
[    0.605762] gpio_intn_value:0
[    0.605871] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.606411] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.606413] IRQ: BC_LVL, handler pending
[    0.606414] IRQ: PD sent good CRC
[    0.606831] PD message header: 966 len:0 crc:ba2667a
[    0.606835] gpio_intn_value:0
[    0.606953] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.607513] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.607517] IRQ: BC_LVL, handler pending
[    0.607526] gpio_intn_value:1
[    0.607639] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.638541] BC_LVL handler, status0=0x92
[    0.657448] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc1, status1: 0x08
[    0.657460] IRQ: BC_LVL, handler pending
[    0.657468] gpio_intn_value:0
[    0.657577] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.658123] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.658126] IRQ: BC_LVL, handler pending
[    0.658128] gpio_intn_value:0
[    0.658237] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.658781] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.658783] IRQ: BC_LVL, handler pending
[    0.658784] IRQ: PD sent good CRC
[    0.659384] PD message header: 1b6f len:4 crc:2bdb29d7
[    0.659393] gpio_intn_value:1
[    0.659505] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.660561] sending PD message header: 524f
[    0.660567] sending PD message len: 20
[    0.661127] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.661131] IRQ: BC_LVL, handler pending
[    0.661136] gpio_intn_value:0
[    0.661245] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.661786] IRQ: 0x01, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.661789] IRQ: BC_LVL, handler pending
[    0.661791] gpio_intn_value:0
[    0.661900] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.662439] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.662441] IRQ: BC_LVL, handler pending
[    0.662442] gpio_intn_value:0
[    0.662551] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.663089] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd2, status1: 0x08
[    0.663091] IRQ: BC_LVL, handler pending
[    0.663093] IRQ: PD tx success
[    0.663511] PD message header: 361 len:0 crc:a43619a3
[    0.663517] gpio_intn_value:0
[    0.663626] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.664180] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.664183] IRQ: BC_LVL, handler pending
[    0.664187] gpio_intn_value:0
[    0.664296] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.664867] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.664870] IRQ: BC_LVL, handler pending
[    0.664873] IRQ: PD sent good CRC
[    0.665477] PD message header: 1d6f len:4 crc:b62e7399
[    0.665489] gpio_intn_value:1
[    0.665603] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.666301] sending PD message header: 244f
[    0.666303] sending PD message len: 8
[    0.667147] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.667150] IRQ: BC_LVL, handler pending
[    0.667157] gpio_intn_value:0
[    0.667266] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.667806] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.667808] IRQ: BC_LVL, handler pending
[    0.667810] gpio_intn_value:0
[    0.667919] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.668485] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd1, status1: 0x08
[    0.668488] IRQ: BC_LVL, handler pending
[    0.668489] IRQ: PD tx success
[    0.668915] PD message header: 561 len:0 crc:4d55bc96
[    0.668923] gpio_intn_value:0
[    0.669037] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.669588] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.669591] IRQ: BC_LVL, handler pending
[    0.669596] gpio_intn_value:0
[    0.669710] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.670256] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.670264] IRQ: BC_LVL, handler pending
[    0.670267] IRQ: PD sent good CRC
[    0.670870] PD message header: 1f6f len:4 crc:6d4976dd
[    0.670885] gpio_intn_value:1
[    0.670999] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.671695] sending PD message header: 264f
[    0.671698] sending PD message len: 8
[    0.672331] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.672334] IRQ: BC_LVL, handler pending
[    0.672340] gpio_intn_value:0
[    0.672449] aw35615_i2c_read AW_REG_STATUS1 :0x20
[    0.672991] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xc2, status1: 0x08
[    0.672993] IRQ: BC_LVL, handler pending
[    0.672996] gpio_intn_value:0
[    0.673105] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.673645] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.673648] IRQ: BC_LVL, handler pending
[    0.673649] IRQ: PD tx success
[    0.674073] PD message header: 761 len:0 crc:a35bddba
[    0.674083] gpio_intn_value:0
[    0.674211] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.674759] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x08
[    0.674762] IRQ: BC_LVL, handler pending
[    0.674766] gpio_intn_value:0
[    0.674906] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.675487] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.675491] IRQ: BC_LVL, handler pending
[    0.675495] IRQ: PD sent good CRC
[    0.676164] PD message header: 116f len:4 crc:4e6c9a32
[    0.676177] gpio_intn_value:1
[    0.676287] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.677123] sending PD message header: 184f
[    0.677127] sending PD message len: 4
[    0.677896] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.677901] IRQ: BC_LVL, handler pending
[    0.677909] gpio_intn_value:0
[    0.678022] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.678727] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.678730] IRQ: BC_LVL, handler pending
[    0.678733] gpio_intn_value:0
[    0.678842] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.679383] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xc1, status1: 0x08
[    0.679385] IRQ: BC_LVL, handler pending
[    0.679387] IRQ: PD tx success
[    0.679806] PD message header: 961 len:0 crc:44e3f0bd
[    0.679813] gpio_intn_value:0
[    0.679922] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.680490] IRQ: 0x51, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.680492] IRQ: BC_LVL, handler pending
[    0.680494] IRQ: PD sent good CRC
[    0.681184] PD message header: 236f len:8 crc:93442acc
[    0.681191] gpio_intn_value:0
[    0.681301] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.681845] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x28
[    0.681847] IRQ: BC_LVL, handler pending
[    0.681851] gpio_intn_value:1
[    0.681959] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.682699] sending PD message header: 2a4f
[    0.682703] sending PD message len: 8
[    0.683460] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x28
[    0.683466] IRQ: BC_LVL, handler pending
[    0.683473] gpio_intn_value:0
[    0.683638] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.684216] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.684222] IRQ: BC_LVL, handler pending
[    0.684234] gpio_intn_value:0
[    0.684355] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.684905] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0xd2, status1: 0x08
[    0.684909] IRQ: BC_LVL, handler pending
[    0.684913] IRQ: PD tx success
[    0.685342] PD message header: b61 len:0 crc:aaed9191
[    0.685355] gpio_intn_value:0
[    0.685467] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.686064] IRQ: 0x51, a: 0x00, b: 0x00, status0: 0xd1, status1: 0x08
[    0.686067] IRQ: BC_LVL, handler pending
[    0.686073] gpio_intn_value:0
[    0.686183] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.686728] IRQ: 0x41, a: 0x00, b: 0x01, status0: 0x92, status1: 0x08
[    0.686731] IRQ: BC_LVL, handler pending
[    0.686732] IRQ: PD sent good CRC
[    0.687427] PD message header: 256f len:8 crc:b62ac3bd
[    0.687433] gpio_intn_value:1
[    0.687544] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.688209] sending PD message header: 1c4f
[    0.688212] sending PD message len: 4
[    0.688841] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x20
[    0.688845] IRQ: BC_LVL, handler pending
[    0.688851] gpio_intn_value:0
[    0.688960] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.689505] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd0, status1: 0x08
[    0.689507] IRQ: BC_LVL, handler pending
[    0.689509] gpio_intn_value:0
[    0.689618] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.690164] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.690167] IRQ: BC_LVL, handler pending
[    0.690168] IRQ: PD tx success
[    0.690589] PD message header: d61 len:0 crc:438e34a4
[    0.690599] gpio_intn_value:1
[    0.690708] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.709149] sending PD message header: 2e4f
[    0.709152] sending PD message len: 8
[    0.709989] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0xd2, status1: 0x28
[    0.709992] IRQ: BC_LVL, handler pending
[    0.709996] gpio_intn_value:0
[    0.710104] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.710643] IRQ: 0x41, a: 0x00, b: 0x00, status0: 0x92, status1: 0x08
[    0.710645] IRQ: BC_LVL, handler pending
[    0.710646] gpio_intn_value:0
[    0.710755] aw35615_i2c_read AW_REG_STATUS1 :0x08
[    0.711293] IRQ: 0x51, a: 0x04, b: 0x00, status0: 0x92, status1: 0x08
[    0.711295] IRQ: BC_LVL, handler pending
[    0.711296] IRQ: PD tx success
[    0.711714] PD message header: f61 len:0 crc:ad805588
[    0.711719] gpio_intn_value:1
[    0.711828] aw35615_i2c_read AW_REG_STATUS1 :0x28
[    0.748631] BC_LVL handler, status0=0x92
