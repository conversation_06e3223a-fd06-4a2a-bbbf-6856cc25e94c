Jan 25 00:00:00 kernel: klogd started: BusyBox v1.25.0 (2025-06-23 10:50:06 CST)
Jan 25 00:00:00 kernel: [    0.000000] Booting Linux on physical CPU 0x0
Jan 25 00:00:00 kernel: [    0.000000] Linux version 4.9.38 (xreal@a15bbc1ccbbe) (gcc version 7.5.0 (Linaro GCC 7.5-2019.12) ) #3 SMP Mon Jun 23 10:47:47 CST 2025
Jan 25 00:00:00 kernel: [    0.000000] Boot CPU: AArch64 Processor [410fd034]
Jan 25 00:00:00 kernel: [    0.000000] Memory limited to 304MB
Jan 25 00:00:00 kernel: [    0.000000] Icc memory setup at 0x0000000024000000 size 0x0000000000000800 KB
Jan 25 00:00:00 kernel: [    0.000000] OF: reserved mem: initialized node icc@0x24000000, compatible id icc-region
Jan 25 00:00:00 kernel: [    0.000000] cma: Reserved 16 MiB at 0x0000000032000000
Jan 25 00:00:00 kernel: [    0.000000] On node 0 totalpages: 77822
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 1216 pages used for memmap
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 0 pages reserved
Jan 25 00:00:00 kernel: [    0.000000]   DMA zone: 77822 pages, LIFO batch:15
Jan 25 00:00:00 kernel: [    0.000000] percpu: Embedded 23 pages/cpu @ffffffc031f60000 s53400 r8192 d32616 u94208
Jan 25 00:00:00 kernel: [    0.000000] pcpu-alloc: s53400 r8192 d32616 u94208 alloc=23*4096
Jan 25 00:00:00 kernel: [    0.000000] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 
Jan 25 00:00:00 kernel: [    0.000000] Detected VIPT I-cache on CPU0
Jan 25 00:00:00 kernel: [    0.000000] CPU features: enabling workaround for ARM erratum 845719
Jan 25 00:00:00 kernel: [    0.000000] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 76606
Jan 25 00:00:00 kernel: [    0.000000] Kernel command line: console=ttyS0,disable,earlyprintk loglevel=1,quiet root=/dev/mmcblk0p19 rootwait rw rootfstype=ext4 gpt mem=304m flagfile=/usrdata/sirius-clean-system-flag nmi_watchdog=panic part_info=34952
Jan 25 00:00:00 kernel: [    0.000000] PID hash table entries: 2048 (order: 2, 16384 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Dentry cache hash table entries: 65536 (order: 7, 524288 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Inode-cache hash table entries: 32768 (order: 6, 262144 bytes)
Jan 25 00:00:00 kernel: [    0.000000] Memory: 225676K/311288K available (7550K kernel code, 450K rwdata, 1820K rodata, 2048K init, 359K bss, 69228K reserved, 16384K cma-reserved)
Jan 25 00:00:00 kernel: [    0.000000] Virtual kernel memory layout:
Jan 25 00:00:00 kernel: [    0.000000]     modules : 0xffffff8000000000 - 0xffffff8008000000   (   128 MB)
Jan 25 00:00:00 kernel: [    0.000000]     vmalloc : 0xffffff8008000000 - 0xffffffbebfff0000   (   250 GB)
Jan 25 00:00:00 kernel: [    0.000000]       .text : 0xffffff80080a0000 - 0xffffff8008800000   (  7552 KB)
Jan 25 00:00:00 kernel: [    0.000000]     .rodata : 0xffffff8008800000 - 0xffffff8008a00000   (  2048 KB)
Jan 25 00:00:00 kernel: [    0.000000]       .init : 0xffffff8008a00000 - 0xffffff8008c00000   (  2048 KB)
Jan 25 00:00:00 kernel: [    0.000000]       .data : 0xffffff8008c00000 - 0xffffff8008c70808   (   451 KB)
Jan 25 00:00:00 kernel: [    0.000000]        .bss : 0xffffff8008c70808 - 0xffffff8008cca79c   (   360 KB)
Jan 25 00:00:00 kernel: [    0.000000]     fixed   : 0xffffffbefe7fd000 - 0xffffffbefec00000   (  4108 KB)
Jan 25 00:00:00 kernel: [    0.000000]     PCI I/O : 0xffffffbefee00000 - 0xffffffbeffe00000   (    16 MB)
Jan 25 00:00:00 kernel: [    0.000000]     vmemmap : 0xffffffbf00000000 - 0xffffffc000000000   (     4 GB maximum)
Jan 25 00:00:00 kernel: [    0.000000]               0xffffffbf00800000 - 0xffffffbf00cc0000   (     4 MB actual)
Jan 25 00:00:00 kernel: [    0.000000]     memory  : 0xffffffc020000000 - 0xffffffc033000000   (   304 MB)
Jan 25 00:00:00 kernel: [    0.000000] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=4, Nodes=1
Jan 25 00:00:00 kernel: [    0.000000] Hierarchical RCU implementation.
Jan 25 00:00:00 kernel: [    0.000000] 	Build-time adjustment of leaf fanout to 64.
Jan 25 00:00:00 kernel: [    0.000000] NR_IRQS:64 nr_irqs:64 0
Jan 25 00:00:00 kernel: [    0.000000] arm_arch_timer: Architected cp15 timer(s) running at 24.00MHz (virt).
Jan 25 00:00:00 kernel: [    0.000000] clocksource: arch_sys_counter: mask: 0xffffffffffffff max_cycles: 0x588fe9dc0, max_idle_ns: 440795202592 ns
Jan 25 00:00:00 kernel: [    0.000002] sched_clock: 56 bits at 24MHz, resolution 41ns, wraps every 4398046511097ns
Jan 25 00:00:00 kernel: [    0.000150] Console: colour dummy device 80x25
Jan 25 00:00:00 kernel: [    0.000164] Calibrating delay loop (skipped), value calculated using timer frequency.. 48.00 BogoMIPS (lpj=240000)
Jan 25 00:00:00 kernel: [    0.000168] pid_max: default: 32768 minimum: 301
Jan 25 00:00:00 kernel: [    0.000213] Mount-cache hash table entries: 1024 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.000215] Mountpoint-cache hash table entries: 1024 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.000662] ASID allocator initialised with 65536 entries
Jan 25 00:00:00 kernel: [    0.001583] Detected VIPT I-cache on CPU1
Jan 25 00:00:00 kernel: [    0.001619] CPU1: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001756] Detected VIPT I-cache on CPU2
Jan 25 00:00:00 kernel: [    0.001767] CPU2: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001889] Detected VIPT I-cache on CPU3
Jan 25 00:00:00 kernel: [    0.001899] CPU3: Booted secondary processor [410fd034]
Jan 25 00:00:00 kernel: [    0.001920] Brought up 4 CPUs
Jan 25 00:00:00 kernel: [    0.001922] SMP: Total of 4 processors activated.
Jan 25 00:00:00 kernel: [    0.001925] CPU features: detected feature: 32-bit EL0 Support
Jan 25 00:00:00 kernel: [    0.001928] CPU: All CPU(s) started at EL1
Jan 25 00:00:00 kernel: [    0.001937] alternatives: patching kernel code
Jan 25 00:00:00 kernel: [    0.002254] devtmpfs: initialized
Jan 25 00:00:00 kernel: [    0.006210] clocksource: jiffies: mask: 0xffffffff max_cycles: 0xffffffff, max_idle_ns: 19112604462750000 ns
Jan 25 00:00:00 kernel: [    0.006222] futex hash table entries: 1024 (order: 5, 131072 bytes)
Jan 25 00:00:00 kernel: [    0.007247] NET: Registered protocol family 16
Jan 25 00:00:00 kernel: [    0.007788] vdso: 2 pages (1 code @ ffffff8008807000, 1 data @ ffffff8008c04000)
Jan 25 00:00:00 kernel: [    0.007796] hw-breakpoint: found 6 breakpoint and 4 watchpoint registers.
Jan 25 00:00:00 kernel: [    0.008131] DMA: preallocated 256 KiB pool for atomic allocations
Jan 25 00:00:00 kernel: [    0.015369] dw_dmac 1e10000.ahb_dma: DesignWare DMA Controller, 8 channels
Jan 25 00:00:00 kernel: [    0.015903] SCSI subsystem initialized
Jan 25 00:00:00 kernel: [    0.015978] usbcore: registered new interface driver usbfs
Jan 25 00:00:00 kernel: [    0.015995] usbcore: registered new interface driver hub
Jan 25 00:00:00 kernel: [    0.016023] usbcore: registered new device driver usb
Jan 25 00:00:00 kernel: [    0.017499] Linux video capture interface: v2.00
Jan 25 00:00:00 kernel: [    0.017671] icc-artosyn icc-artosyn: assigned reserved memory node icc@0x24000000
Jan 25 00:00:00 kernel: [    0.018011] Advanced Linux Sound Architecture Driver Initialized.
Jan 25 00:00:00 kernel: [    0.018367] clocksource: Switched to clocksource arch_sys_counter
Jan 25 00:00:00 kernel: [    0.019551] NET: Registered protocol family 2
Jan 25 00:00:00 kernel: [    0.019794] TCP established hash table entries: 4096 (order: 3, 32768 bytes)
Jan 25 00:00:00 kernel: [    0.019812] TCP bind hash table entries: 4096 (order: 4, 65536 bytes)
Jan 25 00:00:00 kernel: [    0.019873] TCP: Hash tables configured (established 4096 bind 4096)
Jan 25 00:00:00 kernel: [    0.019903] UDP hash table entries: 256 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.019910] UDP-Lite hash table entries: 256 (order: 1, 8192 bytes)
Jan 25 00:00:00 kernel: [    0.019982] NET: Registered protocol family 1
Jan 25 00:00:00 kernel: [    0.020163] RPC: Registered named UNIX socket transport module.
Jan 25 00:00:00 kernel: [    0.020164] RPC: Registered udp transport module.
Jan 25 00:00:00 kernel: [    0.020165] RPC: Registered tcp transport module.
Jan 25 00:00:00 kernel: [    0.020166] RPC: Registered tcp NFSv4.1 backchannel transport module.
Jan 25 00:00:00 kernel: [    0.020537] hw perfevents: enabled with armv8_cortex_a53 PMU driver, 7 counters available
Jan 25 00:00:00 kernel: [    0.021098] workingset: timestamp_bits=62 max_order=16 bucket_order=0
Jan 25 00:00:00 kernel: [    0.024241] exFAT: file-system version 2.2.0-3arter97
Jan 25 00:00:00 kernel: [    0.024532] NFS: Registering the id_resolver key type
Jan 25 00:00:00 kernel: [    0.024548] Key type id_resolver registered
Jan 25 00:00:00 kernel: [    0.024549] Key type id_legacy registered
Jan 25 00:00:00 kernel: [    0.024555] nfs4filelayout_init: NFSv4 File Layout Driver Registering...
Jan 25 00:00:00 kernel: [    0.024570] fuse init (API version 7.26)
Jan 25 00:00:00 kernel: [    0.025779] io scheduler noop registered
Jan 25 00:00:00 kernel: [    0.025781] io scheduler deadline registered
Jan 25 00:00:00 kernel: [    0.025831] io scheduler cfq registered (default)
Jan 25 00:00:00 kernel: [    0.026208] artosyn_typec_comphy_probe 1275 0 ffffffc03083f000
Jan 25 00:00:00 kernel: [    0.026377] artosyn_kuiper_usb2phy_probe 288 0 ffffffc03083f400
Jan 25 00:00:00 kernel: [    0.026439] artosyn_kuiper_usb2phy_probe 288 0 ffffffc03083f800
Jan 25 00:00:00 kernel: [    0.027239] gpio-artosyn a10a000.gpio: get 16 irqs for all ports
Jan 25 00:00:00 kernel: [    0.031257] Serial: 8250/16550 driver, 8 ports, IRQ sharing disabled
Jan 25 00:00:00 kernel: [    0.032022] console [ttyS0] disabled
Jan 25 00:00:00 kernel: [    0.032043] 1500000.serial: ttyS0 at MMIO 0x1500000 (irq = 31, base_baud = 8333333) is a 16550A
Jan 25 00:00:00 kernel: [    0.032069] console [ttyS0] enabled
Jan 25 00:00:00 kernel: [    0.032236] 1504000.serial: ttyS2 at MMIO 0x1504000 (irq = 32, base_baud = 8333333) is a 16550A
Jan 25 00:00:00 kernel: [    0.033852] brd: module loaded
Jan 25 00:00:00 kernel: [    0.034344] loop: module loaded
Jan 25 00:00:00 kernel: [    0.034498] artosyn_dprx_probe 3731 -517
Jan 25 00:00:00 kernel: [    0.035164] fast boot device registered
Jan 25 00:00:00 kernel: [    0.035198] hdcp check device registered
Jan 25 00:00:00 kernel: [    0.035242] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY0 at 0xa098000 with size 0x400
Jan 25 00:00:00 kernel: [    0.035245] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY1 at 0xa098400 with size 0x400
Jan 25 00:00:00 kernel: [    0.035249] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY2 at 0xa09c000 with size 0x400
Jan 25 00:00:00 kernel: [    0.035252] dphy_reg_driver a09800000000400.dphy_reg: Mapped DPHY3 at 0xa09c400 with size 0x400
Jan 25 00:00:00 kernel: [    0.035282] dphy_reg_misc_device registered
Jan 25 00:00:00 kernel: [    0.035352] hw_info:hw_id_chan not get! error:-517
Jan 25 00:00:00 kernel: [    0.035365] ia8201_init called
Jan 25 00:00:00 kernel: [    0.035391] ia8201_probe called
Jan 25 00:00:00 kernel: [    0.035416] ret : 0
Jan 25 00:00:00 kernel: [    0.035420] cdev init
Jan 25 00:00:00 kernel: [    0.035421] cdev add
Jan 25 00:00:00 kernel: [    0.035422] create class
Jan 25 00:00:00 kernel: [    0.035433] device create
Jan 25 00:00:00 kernel: [    0.035726] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.035861] spi_master spi0: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.036008] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.036114] spi_master spi1: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.036221] enter dw_spi_add_host
Jan 25 00:00:00 kernel: [    0.036319] spi_master spi2: will run message pump with realtime priority
Jan 25 00:00:00 kernel: [    0.036623] sony_oled_driver_init^M
Jan 25 00:00:00 kernel: [    0.036636] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.036638] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.036660] Failed to get oled vin regulator
Jan 25 00:00:00 kernel: [    0.036661] init failed
Jan 25 00:00:00 kernel: [    0.036671] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.036673] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.036679] Failed to get oled vin regulator
Jan 25 00:00:00 kernel: [    0.036679] init failed
Jan 25 00:00:00 kernel: [    0.036813] libphy: Fixed MDIO Bus: probed
Jan 25 00:00:00 kernel: [    0.037027] usbcore: registered new interface driver r8152
Jan 25 00:00:00 kernel: [    0.037117] artosyn-dwc3 18.artosyn_dwc3: force disable usb2_using_dwc3
Jan 25 00:00:00 kernel: [    0.037459] 8080000.usb supply vusb_d not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.037487] 8080000.usb supply vusb_a not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.037543] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:00 kernel: [    0.038116] dwc2 8080000.usb: EPs: 16, dedicated fifos, 3968 entries in SPRAM
Jan 25 00:00:00 kernel: [    0.038344] usbcore: registered new interface driver usb-storage
Jan 25 00:00:00 kernel: [    0.038474] hw_info: hw_id_get not cpmplete!!!
Jan 25 00:00:00 kernel: [    0.038475] artosyn_typec_probe hw_id get not complete, re-probe
Jan 25 00:00:00 kernel: [    0.038556] [aw35615] aw35615_probe 1854: aw35615_probe enter
Jan 25 00:00:00 kernel: [    0.038557] hw_info: hw_id_get not cpmplete!!!
Jan 25 00:00:00 kernel: [    0.038558] [aw35615] aw35615_probe 1858: aw35615_probe hw_id get not complete, re-probe
Jan 25 00:00:00 kernel: [    0.038622] i2c /dev entries driver
Jan 25 00:00:00 kernel: [    0.038860] sy_oled_driver_init^M
Jan 25 00:00:00 kernel: [    0.038879] ec_i2c_init 
Jan 25 00:00:00 kernel: [    0.038898] ec major : 0, minor : 0^M
Jan 25 00:00:00 kernel: [    0.038958] ec driver mode:1
Jan 25 00:00:00 kernel: [    0.040107] ec_probe^M
Jan 25 00:00:00 kernel: [    0.040146] usbcore: registered new interface driver uvcvideo
Jan 25 00:00:00 kernel: [    0.040147] USB Video Class driver (1.1.1)
Jan 25 00:00:00 kernel: [    0.040346] dw_wdt 1600000.wdt: using irq(33) mode!
Jan 25 00:00:00 kernel: [    0.041550] sdhci: Secure Digital Host Controller Interface driver
Jan 25 00:00:00 kernel: [    0.041552] sdhci: Copyright(c) Pierre Ossman
Jan 25 00:00:00 kernel: [    0.041553] Synopsys Designware Multimedia Card Interface Driver
Jan 25 00:00:00 kernel: [    0.041681] sdhci-pltfm: SDHCI platform and OF driver helper
Jan 25 00:00:00 kernel: [    0.041856] sdhci-kuiper 8050000.sdhci: Ignore voltage domain gpio.
Jan 25 00:00:00 kernel: [    0.041908] mmc0: Unknown controller version (5). You may experience problems.
Jan 25 00:00:00 kernel: [    0.042248] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 1600000
Jan 25 00:00:00 kernel: [    0.042251] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 1600000
Jan 25 00:00:00 kernel: [    0.042322] mmc0: SDHCI controller on 8050000.sdhci [8050000.sdhci] using ADMA
Jan 25 00:00:00 kernel: [    0.042579] hidraw: raw HID events driver (C) Jiri Kosina
Jan 25 00:00:00 kernel: [    0.042633] usbcore: registered new interface driver usbhid
Jan 25 00:00:00 kernel: [    0.042634] usbhid: USB HID core driver
Jan 25 00:00:00 kernel: [    0.042796] ashmem: initialized
Jan 25 00:00:00 kernel: [    0.043227] spi0.0 supply inven,vdd_ana not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.043263] spi0.0 supply inven,vcc_i2c not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.043291] inv_mpu: inv_mpu_probe: power on here.
Jan 25 00:00:00 kernel: [    0.043292] inv_mpu: inv_mpu_probe: power on.
Jan 25 00:00:00 kernel: [    0.087551] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 24000000
Jan 25 00:00:00 kernel: [    0.087591] clk_composite_determine_rate:144, fix_pll_600 best_parent_hw 600000000
Jan 25 00:00:00 kernel: [    0.087600] clk_composite_determine_rate:144, cgu_oscin_clk best_parent_hw 24000000
Jan 25 00:00:00 kernel: [    0.087601] clk_composite_determine_rate:144, fix_pll_600 best_parent_hw 600000000
Jan 25 00:00:00 kernel: [    0.087714] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 0
Jan 25 00:00:00 kernel: [    0.087767] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 1
Jan 25 00:00:00 kernel: [    0.087822] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 2
Jan 25 00:00:00 kernel: [    0.087877] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 3
Jan 25 00:00:00 kernel: [    0.087932] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 4
Jan 25 00:00:00 kernel: [    0.087988] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 5
Jan 25 00:00:00 kernel: [    0.088091] sdhci-kuiper 8050000.sdhci: mmc0: Found good phase = 7
Jan 25 00:00:00 kernel: [    0.088116] sdhci-kuiper 8050000.sdhci: mmc0: Setting the tuning phase to 2
Jan 25 00:00:00 kernel: [    0.088163] mmc0: new HS200 MMC card at address 0001
Jan 25 00:00:00 kernel: [    0.088369] mmcblk0: mmc0:0001 58A421 3.65 GiB 
Jan 25 00:00:00 kernel: [    0.088444] mmcblk0boot0: mmc0:0001 58A421 partition 1 4.00 MiB
Jan 25 00:00:00 kernel: [    0.088499] mmcblk0boot1: mmc0:0001 58A421 partition 2 4.00 MiB
Jan 25 00:00:00 kernel: [    0.088557] mmcblk0rpmb: mmc0:0001 58A421 partition 3 16.0 MiB
Jan 25 00:00:00 kernel: [    0.089981] Alternate GPT is invalid, using primary GPT.
Jan 25 00:00:00 kernel: [    0.090010]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22
Jan 25 00:00:00 kernel: [    0.158434] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.158463] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.158481] inv_mpu: whoami= 0
Jan 25 00:00:00 kernel: [    0.158497] inv-mpu-iio-spi spi0.0: inv_mpu_probe failed -19
Jan 25 00:00:00 kernel: [    0.158570] spi1.0 supply inven,vdd_ana not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.158599] spi1.0 supply inven,vcc_i2c not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.158644] inv_mpu: inv_mpu_probe: power on here.
Jan 25 00:00:00 kernel: [    0.158645] inv_mpu: inv_mpu_probe: power on.
Jan 25 00:00:00 kernel: [    0.278406] inv_mpu: whoami= e9
Jan 25 00:00:00 kernel: [    0.308945] random: fast init done
Jan 25 00:00:00 kernel: [    0.309658] inv_mpu: data_new=0 40, 4, 40,8
Jan 25 00:00:00 kernel: [    0.309782] inv_mpu: external clkin enable
Jan 25 00:00:00 kernel: [    0.309818] inv_mpu: INT2 pin mux set to CLKIN ret = 0
Jan 25 00:00:00 kernel: [    0.309852] inv_mpu: RTC MODE enable ret = 0
Jan 25 00:00:00 kernel: [    0.310055] inv_mpu: I3C STC MODE disable ret = 0
Jan 25 00:00:00 kernel: [    0.310259] inv_mpu: accel source set ret = 0
Jan 25 00:00:00 kernel: [    0.310463] inv_mpu: gyro source set ret = 0
Jan 25 00:00:00 kernel: [    0.310498] inv_mpu: AUX2 disable ret = 0
Jan 25 00:00:00 kernel: [    0.310499] inv_mpu: external clkin enable result is 0
Jan 25 00:00:00 kernel: [    0.318725] inv_mpu: write mag matrix data
Jan 25 00:00:00 kernel: [    0.324132] inv_mpu: inv_mpu_initialize: initialize result is 0....
Jan 25 00:00:00 kernel: [    0.324421] inv_mpu: wakeup_source is created successfully
Jan 25 00:00:00 kernel: [    0.324424] inv-mpu-iio-spi spi1.0: icm45600 ma-kernel-10.2.4 is ready to go!
Jan 25 00:00:00 kernel: [    0.324426] inv-mpu-iio-spi spi1.0: inv-mpu-iio clock type 1
Jan 25 00:00:00 kernel: [    0.324427] inv_mpu: Data read from FIFO
Jan 25 00:00:00 kernel: [    0.324494] mmc5603 5-0030: enter mmc5603_probe
Jan 25 00:00:00 kernel: [    0.324799] mmc5603 5-0030: MMC5603 chip id 10
Jan 25 00:00:00 kernel: [    0.341456] NET: Registered protocol family 10
Jan 25 00:00:00 kernel: [    0.341820] sit: IPv6, IPv4 and MPLS over IPv4 tunneling driver
Jan 25 00:00:00 kernel: [    0.342054] NET: Registered protocol family 17
Jan 25 00:00:00 kernel: [    0.342095] Key type dns_resolver registered
Jan 25 00:00:00 kernel: [    0.345639] artosyn_dprx_probe 3731 -517
Jan 25 00:00:00 kernel: [    0.345766] hw_info: hw_id_chan get!
Jan 25 00:00:00 kernel: [    0.350146] hw_info:read hw_id raw :1469
Jan 25 00:00:00 kernel: [    0.350224] Misc device registered: hw_info, minor number = 1013
Jan 25 00:00:00 kernel: [    0.350249] sony_oled_probe-0
Jan 25 00:00:00 kernel: [    0.350251] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.350296] of_get_named_gpio = 77
Jan 25 00:00:00 kernel: [    0.350299] gpio gpio-rst-77 request ok
Jan 25 00:00:00 kernel: [    0.350300] dev->rst_gpio77
Jan 25 00:00:00 kernel: [    0.360302] find label: ecx343_left
Jan 25 00:00:00 kernel: [    0.360397] check spi communication ok!
Jan 25 00:00:00 kernel: [    0.360401] major : 246, minor : 0
Jan 25 00:00:00 kernel: [    0.360403] cdev add successfully
Jan 25 00:00:00 kernel: [    0.360414] class create successfully
Jan 25 00:00:00 kernel: [    0.360452] device create successfully
Jan 25 00:00:00 kernel: [    0.360473] sony_oled_probe-1
Jan 25 00:00:00 kernel: [    0.360474] compatible: sony,ecx343
Jan 25 00:00:00 kernel: [    0.360495] of_get_named_gpio = 28
Jan 25 00:00:00 kernel: [    0.360496] gpio gpio-rst-28 request ok
Jan 25 00:00:00 kernel: [    0.360497] dev->rst_gpio28
Jan 25 00:00:00 kernel: [    0.370499] find label: ecx343_right
Jan 25 00:00:00 kernel: [    0.370575] check spi communication ok!
Jan 25 00:00:00 kernel: [    0.370576] major : 246, minor : 1
Jan 25 00:00:00 kernel: [    0.370577] cdev add successfully
Jan 25 00:00:00 kernel: [    0.370588] class create successfully
Jan 25 00:00:00 kernel: [    0.370626] device create successfully
Jan 25 00:00:00 kernel: [    0.370681] hw_info: hw_id : 6
Jan 25 00:00:00 kernel: [    0.370682] artosyn_typec_probe hw_id:6,pd disable
Jan 25 00:00:00 kernel: [    0.370757] artosyn_tcpc soc_internal_pd_disabled: 1
Jan 25 00:00:00 kernel: [    0.370829] artosyn_tcpc typec_base: ffffff8008f9a000, pd_base: ffffff8008fb2000
Jan 25 00:00:00 kernel: [    0.370831] artosyn_typec_probe 1860 success! ffffffc0312a8c10 ffffffc03083ec00 ffffffc030b7f9c0
Jan 25 00:00:00 kernel: [    0.370856] [aw35615] aw35615_probe 1854: aw35615_probe enter
Jan 25 00:00:00 kernel: [    0.370858] hw_info: hw_id : 6
Jan 25 00:00:00 kernel: [    0.370860] [aw35615] aw35615_probe 1866: aw35615_probe hw_id:6,aw ic exsit, continue
Jan 25 00:00:00 kernel: [    0.370993] tcpc_aw35615 2-0022: vid is correct, 0x91
Jan 25 00:00:00 kernel: [    0.371002] 2-0022 supply vbus not found, using dummy regulator
Jan 25 00:00:00 kernel: [    0.371083] xreal-PD Driver: aw35615_debugfs_init debugfs :aw35615-2-0022
Jan 25 00:00:00 kernel: [    0.371718] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.371878] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.371890] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.371940] connector altmodes
Jan 25 00:00:00 kernel: [    0.371941] altmodes altmodes
Jan 25 00:00:00 kernel: [    0.371944] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.371951] OF: graph: no port node found in /soc/i2c2@01204000/aw35615@22/connector
Jan 25 00:00:00 kernel: [    0.374615] artosyn_set_orientation 768 0
Jan 25 00:00:00 kernel: [    0.374623] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.374626] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.374892] [aw35615] aw35615_probe 1977: probe ok
Jan 25 00:00:00 kernel: [    0.374974] artosyn_typec_reset_assert 1517 5
Jan 25 00:00:00 kernel: [    0.375206] dprx-artosyn a0a0000.dp: edid form ddr : magic mismatch
Jan 25 00:00:00 kernel: [    0.375212] dprx-artosyn a0a0000.dp: no valid edid in mmc, try to get edid from dts
Jan 25 00:00:00 kernel: [    0.375358] try load hdcp ret : 0, hdcp_key_length : 685
Jan 25 00:00:00 kernel: [    0.375361] dprx-artosyn a0a0000.dp: hdcp firmware load.
Jan 25 00:00:00 kernel: [    0.375556] artosyn_typec_event 1631 0!
Jan 25 00:00:00 kernel: [    0.375574] artosyn_dprx_probe 3949 success ffffffc03083ec00           (null)
Jan 25 00:00:00 kernel: [    0.375585] artosyn_dprx_dp_pd_event_work 2879 0
Jan 25 00:00:00 kernel: [    0.375870] input: gpio-keys as /devices/platform/gpio-keys/input/input0
Jan 25 00:00:00 kernel: [    0.375993] hctosys: unable to open rtc device (rtc0)
Jan 25 00:00:00 kernel: [    0.376000] artosyn_set_orientation 768 0
Jan 25 00:00:00 kernel: [    0.376012] artosyn_set_mux 799 mode 0 polarity 0
Jan 25 00:00:00 kernel: [    0.376015] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.376119] vmmc sdmmc1: disabling
Jan 25 00:00:00 kernel: [    0.376121] vqmmc sdmmc0: disabling
Jan 25 00:00:00 kernel: [    0.376123] vqmmc sdmmc1: disabling
Jan 25 00:00:00 kernel: [    0.376126] vqmmc sdhci: disabling
Jan 25 00:00:00 kernel: [    0.376128] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.376131] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.376133] eta355t: disabling
Jan 25 00:00:00 kernel: [    0.376136] ALSA device list:
Jan 25 00:00:00 kernel: [    0.376137]   No soundcards found.
Jan 25 00:00:00 kernel: [    0.418202] EXT4-fs (mmcblk0p19): recovery complete
Jan 25 00:00:00 kernel: [    0.418314] EXT4-fs (mmcblk0p19): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.418346] VFS: Mounted root (ext4 filesystem) on device 259:11.
Jan 25 00:00:00 kernel: [    0.418954] Freeing unused kernel memory: 2048K (ffffffc020a00000 - ffffffc020c00000)
Jan 25 00:00:00 kernel: [    0.437186] EXT4-fs (mmcblk0p19): re-mounted. Opts: data=ordered
Jan 25 00:00:00 kernel: [    0.514508] EXT4-fs (mmcblk0p5): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.574923] EXT4-fs (mmcblk0p3): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.588971] artosyn_set_orientation 768 2
Jan 25 00:00:00 kernel: [    0.589014] artosyn_set_mux 799 mode 1 polarity 1
Jan 25 00:00:00 kernel: [    0.589021] artosyn-tcpc a124000.tcpc: dp altmode not register
Jan 25 00:00:00 kernel: [    0.634430] EXT4-fs (mmcblk0p21): mounted filesystem with ordered data mode. Opts: (null)
Jan 25 00:00:00 kernel: [    0.676402] typec_displayport_sink port0-partner.0: dp_sink_altmode_probe 552 ffffffc03083ec00
Jan 25 00:00:00 kernel: [    0.676423] dp_sink_altmode_vdm 318 cmdt 0 cmd 4 hdr ff018104
Jan 25 00:00:00 kernel: [    0.676425] dp_sink_altmode_vdm 320 vdo 30b96680
Jan 25 00:00:00 kernel: [    0.676438] dp_sink_altmode_vdm 375 state 1
Jan 25 00:00:00 kernel: [    0.681219] dp_sink_altmode_vdm 318 cmdt 0 cmd 16 hdr ff018110
Jan 25 00:00:00 kernel: [    0.681224] dp_sink_altmode_vdm 320 vdo 1
Jan 25 00:00:00 kernel: [    0.681231] dp_sink_altmode_vdm 375 state 2
Jan 25 00:00:00 kernel: [    0.687463] dp_sink_altmode_vdm 318 cmdt 0 cmd 17 hdr ff018111
Jan 25 00:00:00 kernel: [    0.687467] dp_sink_altmode_vdm 320 vdo 406
Jan 25 00:00:00 kernel: [    0.687473] dp_sink_altmode_vdm 375 state 3
Jan 25 00:00:00 kernel: [    0.687484] artosyn_set_mux 799 mode 0 polarity 1
Jan 25 00:00:00 kernel: [    0.687497] dp_sink_altmode_configure_vdm 191 0x406
Jan 25 00:00:00 kernel: [    0.687503] dp_sink_altmode_notify 106 0x4 2 4
Jan 25 00:00:00 kernel: [    0.687504] artosyn_set_mux 799 mode 4 polarity 1
Jan 25 00:00:00 kernel: [    0.687512] artosyn_typec_event 1631 1!
Jan 25 00:00:00 kernel: [    0.687518] dp_sink_altmode_event 485 1 dp->hpd:0 hpd:0 irq:0
Jan 25 00:00:00 kernel: [    0.687574] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:00 kernel: [    0.687578] dp random: fb12413dcd55240c
Jan 25 00:00:00 kernel: [    0.687600] artosyn_dprx_init_phy 813 get lane info 4 4 1!
Jan 25 00:00:00 kernel: [    0.687605] artosyn_typec_comphy_init 627
Jan 25 00:00:00 kernel: [    0.687654] artosyn_typec_comphy_set_mode 713 dp mode
Jan 25 00:00:00 kernel: [    0.687712] artosyn_typec_reset_deassert 1546 5
Jan 25 00:00:00 kernel: [    0.708393] artosyn_dprx_init_ctrl 1225 success
Jan 25 00:00:00 kernel: [    0.708401] artosyn_typec_event 1631 1!
Jan 25 00:00:00 kernel: [    0.708407] dp_sink_altmode_event 485 1 dp->hpd:0 hpd:1 irq:0
Jan 25 00:00:00 kernel: [    0.708412] dp_sink_altmode_attention_vdm 300
Jan 25 00:00:00 kernel: [    0.708442] state change 0 -> 1
Jan 25 00:00:00 kernel: [    0.708447] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:00 kernel: [    0.721383] Adding 262140k swap on /usrdata/swapfile.  Priority:-1 extents:3 across:278524k SS
Jan 25 00:00:00 kernel: [    0.728929] ar_mpp_drv: loading out-of-tree module taints kernel.
Jan 25 00:00:00 kernel: [    0.742284] Module osal: init ok
Jan 25 00:00:00 kernel: [    0.742291] Media Memory Zone Manager
Jan 25 00:00:00 kernel: [    0.742436] osal 1.0 init success!
Jan 25 00:00:00 kernel: [    0.750511] ar_vb_init 0
Jan 25 00:00:00 kernel: [    0.752989] ar_sys_init 0
Jan 25 00:00:00 kernel: [    0.757046] camera_pwr_ioctl_init
Jan 25 00:00:00 kernel: [    0.757231] camera_pwr_ioctl_probe
Jan 25 00:00:00 kernel: [    0.757276] get camera_pwr success
Jan 25 00:00:00 kernel: [    0.757426] get camera_rst success
Jan 25 00:00:00 kernel: [    0.758051] camera_pwr_ioctl_probe complete.
Jan 25 00:00:00 kernel: [    0.760105]  imx681 timestamp_record_exp_init
Jan 25 00:00:00 kernel: [    0.760286]  ts_driver_probe
Jan 25 00:00:00 kernel: [    0.760332] get gpios_imx681_exp[27] success
Jan 25 00:00:00 kernel: [    0.760335] irq for gpio[27],irq[83]
Jan 25 00:00:00 kernel: [    0.762877]  imx681 camera_plug_detect_record_init
Jan 25 00:00:00 kernel: [    0.763068]  Camera_plug_detect driver probe
Jan 25 00:00:00 kernel: [    0.763110] get Camera plug detect gpio[13] success
Jan 25 00:00:00 kernel: [    0.763113] Camera plug detect irq for gpio[13],irq[69]
Jan 25 00:00:00 kernel: [    0.763933] camera plug_in_out_event_process_thread is running
Jan 25 00:00:00 kernel: [    0.765283] VO_PACK Init Start1...
Jan 25 00:00:00 kernel: [    0.765438] VO_PACK probe start.
Jan 25 00:00:00 kernel: [    0.765465] dev_0 start:8820000 size:100000 base: ffffff8009200000.
Jan 25 00:00:00 kernel: [    0.765508] dev0 irq:44 .
Jan 25 00:00:00 kernel: [    0.765521] dev_1 start:8840000 size:100000 base: ffffff8009400000.
Jan 25 00:00:00 kernel: [    0.765526] dev1 irq:45 .
Jan 25 00:00:00 kernel: [    0.765643] VO_PACK probe end.
Jan 25 00:00:00 kernel: [    0.828668] iqs323_init
Jan 25 00:00:00 kernel: [    0.828836] iqs323 3-0044: gpio gpio-rdy-57 request ok
Jan 25 00:00:00 kernel: [    0.873308] power up done 1
Jan 25 00:00:00 kernel: [    0.881692] dprx-artosyn a0a0000.dp: rd 0x0
Jan 25 00:00:00 kernel: [    0.881700] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 0
Jan 25 00:00:00 kernel: [    0.881938] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.881942] dprx-artosyn a0a0000.dp: wr 0 0 0 16
Jan 25 00:00:00 kernel: [    0.882075] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.882404] dprx-artosyn a0a0000.dp: rd 0x0
Jan 25 00:00:00 kernel: [    0.882406] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 0
Jan 25 00:00:00 kernel: [    0.882663] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 0
Jan 25 00:00:00 kernel: [    0.882667] dprx-artosyn a0a0000.dp: wr 0 0 0 16
Jan 25 00:00:00 kernel: [    0.884781] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.885080] dprx-artosyn a0a0000.dp: rd 0x10
Jan 25 00:00:00 kernel: [    0.885088] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 16
Jan 25 00:00:00 kernel: [    0.885368] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 16
Jan 25 00:00:00 kernel: [    0.885373] dprx-artosyn a0a0000.dp: wr 0 16 0 16
Jan 25 00:00:00 kernel: [    0.887492] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.887774] dprx-artosyn a0a0000.dp: rd 0x20
Jan 25 00:00:00 kernel: [    0.887782] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 32
Jan 25 00:00:00 kernel: [    0.887976] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 32
Jan 25 00:00:00 kernel: [    0.887979] dprx-artosyn a0a0000.dp: wr 0 32 0 16
Jan 25 00:00:00 kernel: [    0.888458] iqs323 3-0044: gpio_num_57: using irq 113 for Cap Sensor rdy signal detection
Jan 25 00:00:00 kernel: [    0.888464] iqs323 3-0044: iqs323_reset is ready! 
Jan 25 00:00:00 kernel: [    0.888709] iqs323 3-0044: iqs323_probe is over : 0
Jan 25 00:00:00 kernel: [    0.890109] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.890355] dprx-artosyn a0a0000.dp: rd 0x30
Jan 25 00:00:00 kernel: [    0.890363] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 48
Jan 25 00:00:00 kernel: [    0.890535] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 48
Jan 25 00:00:00 kernel: [    0.890539] dprx-artosyn a0a0000.dp: wr 0 48 0 16
Jan 25 00:00:00 kernel: [    0.892651] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.892885] dprx-artosyn a0a0000.dp: rd 0x40
Jan 25 00:00:00 kernel: [    0.892900] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 64
Jan 25 00:00:00 kernel: [    0.893067] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 64
Jan 25 00:00:00 kernel: [    0.893071] dprx-artosyn a0a0000.dp: wr 0 64 0 16
Jan 25 00:00:00 kernel: [    0.895182] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.895425] dprx-artosyn a0a0000.dp: rd 0x50
Jan 25 00:00:00 kernel: [    0.895433] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 80
Jan 25 00:00:00 kernel: [    0.895618] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 80
Jan 25 00:00:00 kernel: [    0.895623] dprx-artosyn a0a0000.dp: wr 0 80 0 16
Jan 25 00:00:00 kernel: [    0.897292] ar_vb_open
Jan 25 00:00:00 kernel: [    0.897353] VB_EXITMCPL: not is_inited yet
Jan 25 00:00:00 kernel: [    0.897493] ar_vb_release
Jan 25 00:00:00 kernel: [    0.897546] ar_vb_open
Jan 25 00:00:00 kernel: [    0.897590] Create 0 common pools
Jan 25 00:00:00 kernel: [    0.897745] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.897986] dprx-artosyn a0a0000.dp: rd 0x60
Jan 25 00:00:00 kernel: [    0.897991] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 96
Jan 25 00:00:00 kernel: [    0.898173] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 96
Jan 25 00:00:00 kernel: [    0.898177] dprx-artosyn a0a0000.dp: wr 0 96 0 16
Jan 25 00:00:00 kernel: [    0.900291] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.900533] dprx-artosyn a0a0000.dp: rd 0x70
Jan 25 00:00:00 kernel: [    0.900538] dprx-artosyn a0a0000.dp: IC_INTR_R_RX_FULL 1 112
Jan 25 00:00:00 kernel: [    0.900709] dprx-artosyn a0a0000.dp: IC_INTR_R_RD_REQ 112
Jan 25 00:00:00 kernel: [    0.900713] dprx-artosyn a0a0000.dp: wr 0 112 0 16
Jan 25 00:00:00 kernel: [    0.902828] dprx-artosyn a0a0000.dp: IC_INTR_R_STOP_DET
Jan 25 00:00:00 kernel: [    0.909198] state change 1 -> 2
Jan 25 00:00:00 kernel: [    0.909213] pending state change 2 -> 3 @ 1000 ms
Jan 25 00:00:00 kernel: [    0.909220] link rate change 10000
Jan 25 00:00:00 kernel: [    0.909222] PHY 0x744: 068fc180
Jan 25 00:00:00 kernel: [    0.909223] 0x2208: 000000ff
Jan 25 00:00:00 kernel: [    0.909224] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    0.909225] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    0.909226] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.909227] 0x240c: 00000000
Jan 25 00:00:00 kernel: [    0.909228] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    0.909230] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    0.909230] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.909232] 0x1134: 01010000
Jan 25 00:00:00 kernel: [    0.909233] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.909234] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.909235] 0x1140: 8003f000
Jan 25 00:00:00 kernel: [    0.909236] PHY 0x3d8: 00000008
Jan 25 00:00:00 kernel: [    0.909237] PHY 0x43d8: 00000008
Jan 25 00:00:00 kernel: [    0.909238] PHY 0x83d8: 00000008
Jan 25 00:00:00 kernel: [    0.909239] PHY 0xc3d8: 00000008
Jan 25 00:00:00 kernel: [    0.909240] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    0.909242] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    0.909243] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    0.909244] PHY 0x73c: 0000081f
Jan 25 00:00:00 kernel: [    0.909245] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    0.909249] link count change 20000
Jan 25 00:00:00 kernel: [    0.909250] PHY 0x744: 068fc180
Jan 25 00:00:00 kernel: [    0.909251] 0x2208: 000000ff
Jan 25 00:00:00 kernel: [    0.909252] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    0.909253] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    0.909255] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.909255] 0x240c: 00000000
Jan 25 00:00:00 kernel: [    0.909257] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    0.909258] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    0.909259] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.909260] 0x1134: 01010000
Jan 25 00:00:00 kernel: [    0.909261] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.909262] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.909263] 0x1140: 8003f000
Jan 25 00:00:00 kernel: [    0.909264] PHY 0x3d8: 00000008
Jan 25 00:00:00 kernel: [    0.909265] PHY 0x43d8: 00000008
Jan 25 00:00:00 kernel: [    0.909266] PHY 0x83d8: 00000008
Jan 25 00:00:00 kernel: [    0.909267] PHY 0xc3d8: 00000008
Jan 25 00:00:00 kernel: [    0.909268] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    0.909269] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    0.909270] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    0.909271] PHY 0x73c: 0000081f
Jan 25 00:00:00 kernel: [    0.909273] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    0.910090] state change 2 -> 3
Jan 25 00:00:00 kernel: [    0.910105] pending state change 3 -> 4 @ 1000 ms
Jan 25 00:00:00 kernel: [    0.910113] training pattern change 1000,rate a,cnt 1, pattern 1
Jan 25 00:00:00 kernel: [    0.910572] artosyn_typec_reset_assert 1517 5
Jan 25 00:00:00 kernel: [    0.910592] artosyn_typec_comphy_configure 881 conf 4 2700
Jan 25 00:00:00 kernel: [    0.910601] artosyn_dprx_init_phy 813 get lane info 4 4 1!
Jan 25 00:00:00 kernel: [    0.910606] artosyn_typec_reset_deassert 1546 5
Jan 25 00:00:00 kernel: [    0.910627] 0x2208: 000f0fff
Jan 25 00:00:00 kernel: [    0.910629] 0x2400: 00000000
Jan 25 00:00:00 kernel: [    0.910630] 0x2404: 00000000
Jan 25 00:00:00 kernel: [    0.910631] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.910632] 0x240c: ffffffff
Jan 25 00:00:00 kernel: [    0.910633] 0x1104: 00000000
Jan 25 00:00:00 kernel: [    0.910634] 0x1108: 01000000
Jan 25 00:00:00 kernel: [    0.910635] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.910636] 0x1134: 0401010a
Jan 25 00:00:00 kernel: [    0.910637] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.910638] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.910640] 0x1140: 8008ff00
Jan 25 00:00:00 kernel: [    0.911574] state change 3 -> 6
Jan 25 00:00:00 kernel: [    0.911592] pending state change 6 -> 7 @ 1000 ms
Jan 25 00:00:00 kernel: [    0.911600] training pattern change 1000,rate a,cnt 4, pattern 7
Jan 25 00:00:00 kernel: [    0.911602] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    0.911603] 0x2400: 00000055
Jan 25 00:00:00 kernel: [    0.911604] 0x2404: 00000055
Jan 25 00:00:00 kernel: [    0.911605] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.911606] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    0.911607] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    0.911608] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    0.911609] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.911611] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    0.911612] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.911613] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.911614] 0x1140: 8009ff00
Jan 25 00:00:00 kernel: [    0.912742] smartPA_init 
Jan 25 00:00:00 kernel: [    0.912826] smartPA_probe slave addr 52
Jan 25 00:00:00 kernel: [    0.912897] of_get_named_gpio = 79
Jan 25 00:00:00 kernel: [    0.912909] find label: HIGH
Jan 25 00:00:00 kernel: [    0.912913] gpio_direction_output: HIGH
Jan 25 00:00:00 kernel: [    0.912916] find label: smartPA_L
Jan 25 00:00:00 kernel: [    0.932201] training pattern change 1000,rate a,cnt 4, pattern 0
Jan 25 00:00:00 kernel: [    0.932207] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    0.932209] 0x2400: 00000377
Jan 25 00:00:00 kernel: [    0.932210] 0x2404: 00000377
Jan 25 00:00:00 kernel: [    0.932211] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.932212] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    0.932213] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    0.932214] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    0.932215] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.932216] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    0.932217] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.932218] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.932219] 0x1140: 800cff00
Jan 25 00:00:00 kernel: [    0.932226] state change 6 -> 7
Jan 25 00:00:00 kernel: [    0.932232] training completed 10(0xa 0xf:4)
Jan 25 00:00:00 kernel: [    0.932234] PHY 0x744: 088fc500
Jan 25 00:00:00 kernel: [    0.932235] 0x2208: 000fffff
Jan 25 00:00:00 kernel: [    0.932236] 0x2400: 00000377
Jan 25 00:00:00 kernel: [    0.932237] 0x2404: 00000377
Jan 25 00:00:00 kernel: [    0.932238] 0x2408: 00000000
Jan 25 00:00:00 kernel: [    0.932239] 0x240c: fafafafa
Jan 25 00:00:00 kernel: [    0.932240] 0x1104: 0000000f
Jan 25 00:00:00 kernel: [    0.932241] 0x1108: 040f0000
Jan 25 00:00:00 kernel: [    0.932242] 0x1118: 00000000
Jan 25 00:00:00 kernel: [    0.932243] 0x1134: 0404010a
Jan 25 00:00:00 kernel: [    0.932245] 0x1138: 00000000
Jan 25 00:00:00 kernel: [    0.932246] 0x113c: 00000000
Jan 25 00:00:00 kernel: [    0.932247] 0x1140: 800cff00
Jan 25 00:00:00 kernel: [    0.932248] PHY 0x3d8: 00000000
Jan 25 00:00:00 kernel: [    0.932249] PHY 0x43d8: 00000010
Jan 25 00:00:00 kernel: [    0.932250] PHY 0x83d8: 00000000
Jan 25 00:00:00 kernel: [    0.932252] PHY 0xc3d8: 00000000
Jan 25 00:00:00 kernel: [    0.932253] PHY 0x1414: 40400040
Jan 25 00:00:00 kernel: [    0.932254] PHY 0x44: 00000000
Jan 25 00:00:00 kernel: [    0.932255] PHY 0x20: 00006080
Jan 25 00:00:00 kernel: [    0.932256] PHY 0x73c: 0000003f
Jan 25 00:00:00 kernel: [    0.932257] PHY 0x14: 60692540
Jan 25 00:00:00 kernel: [    0.932260] Err info 0x0 0x0, 0x0 0x0, 0x0 0x0, 0x0 0x0
Jan 25 00:00:00 kernel: [    0.937747] hil_mmb_alloc pa:0x33284000 len:204800!
Jan 25 00:00:00 kernel: [    0.937978] hil_mmb_alloc pa:0x332b6000 len:4096!
Jan 25 00:00:00 kernel: [    0.938005] hil_mmb_alloc pa:0x332b7000 len:37273600!
Jan 25 00:00:00 kernel: [    0.938026] hil_mmb_alloc pa:0x35643000 len:2768896!
Jan 25 00:00:00 kernel: [    0.938046] hil_mmb_alloc pa:0x358e7000 len:66600960!
Jan 25 00:00:00 kernel: [    0.938066] hil_mmb_alloc pa:0x3986b000 len:8847360!
Jan 25 00:00:00 kernel: [    0.938085] hil_mmb_alloc pa:0x3a0db000 len:25677824!
Jan 25 00:00:00 kernel: [    0.938105] hil_mmb_alloc pa:0x3b958000 len:81920!
Jan 25 00:00:00 kernel: [    1.039668] iqs323 3-0044: iqs323_irq_work:product_number = 1106 
Jan 25 00:00:00 kernel: [    1.043025] iqs323 3-0044: iqs323_irq_work:major_version = 1 
Jan 25 00:00:00 kernel: [    1.046385] iqs323 3-0044: iqs323_irq_work:minor_version = 3 
Jan 25 00:00:00 kernel: [    1.048454] artosyn_dprx_irq_thread_1 sdp start
Jan 25 00:00:00 kernel: [    1.049732] iqs323 3-0044: iqs323_irq_work:iqs323 has been reset
Jan 25 00:00:00 kernel: [    1.068258] using random self ethernet address
Jan 25 00:00:00 kernel: [    1.068267] using random host ethernet address
Jan 25 00:00:00 kernel: [    1.071858] using random self ethernet address
Jan 25 00:00:00 kernel: [    1.071866] using random host ethernet address
Jan 25 00:00:00 kernel: [    1.083006] file system registered
Jan 25 00:00:00 kernel: [    1.087096] Mass Storage Function, version: 2009/09/11
Jan 25 00:00:00 kernel: [    1.087108] LUN: removable file: (no medium)
Jan 25 00:00:00 kernel: [    1.089915] Read iProduct:XREAL One
Jan 25 00:00:00 kernel: [    1.090637] usb0: HOST MAC fc:d2:b6:ad:cc:6d
Jan 25 00:00:00 kernel: [    1.090684] usb0: MAC fc:d2:b6:ad:cc:6a
Jan 25 00:00:00 kernel: [    1.091190] usb1: HOST MAC fc:d2:b6:ad:cc:6c
Jan 25 00:00:00 kernel: [    1.091224] usb1: MAC fc:d2:b6:ad:cc:6b
Jan 25 00:00:00 kernel: [    1.091928] Read iProduct:XREAL One
Jan 25 00:00:00 kernel: [    1.092237] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:00 kernel: [    1.092269] dwc2 8080000.usb: bound driver configfs-gadget
Jan 25 00:00:00 kernel: [    1.097598] iqs323 3-0044: iqs323_config is over : 0
Jan 25 00:00:00 kernel: [    1.104373] ec major : 237, minor : 0^M
Jan 25 00:00:00 kernel: [    1.104387] cdev_add su 
Jan 25 00:00:00 kernel: [    1.104449] class_create su 
Jan 25 00:00:00 kernel: [    1.104622] device_create su 
Jan 25 00:00:00 kernel: [    1.104732] smartPA_probe slave addr 53
Jan 25 00:00:00 kernel: [    1.104795] of_get_named_gpio = 44
Jan 25 00:00:00 kernel: [    1.104813] find label: HIGH
Jan 25 00:00:00 kernel: [    1.104821] gpio_direction_output: HIGH
Jan 25 00:00:00 kernel: [    1.104826] find label: smartPA_R
Jan 25 00:00:00 kernel: [    1.121645] IPv6: ADDRCONF(NETDEV_UP): usb0: link is not ready
Jan 25 00:00:00 kernel: [    1.134300] IPv6: ADDRCONF(NETDEV_UP): usb1: link is not ready
Jan 25 00:00:00 dhcpd: Internet Systems Consortium DHCP Server 4.4.3-P1
Jan 25 00:00:00 dhcpd: Copyright 2004-2022 Internet Systems Consortium.
Jan 25 00:00:00 dhcpd: All rights reserved.
Jan 25 00:00:00 dhcpd: For info, please visit https://www.isc.org/software/dhcp/
Jan 25 00:00:00 dhcpd: Wrote 0 leases to leases file.
Jan 25 00:00:00 kernel: [    1.272177] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:00 kernel: [    1.276673] ec major : 237, minor : 1^M
Jan 25 00:00:00 kernel: [    1.276684] cdev_add su 
Jan 25 00:00:00 kernel: [    1.276742] class_create su 
Jan 25 00:00:00 kernel: [    1.276898] device_create su 
Jan 25 00:00:00 kernel: [    1.291321] vsc 28000
Jan 25 00:00:00 kernel: [    1.291331] video change
Jan 25 00:00:00 kernel: [    1.291333] format 0->0
Jan 25 00:00:00 kernel: [    1.291334] depth 0->1
Jan 25 00:00:00 kernel: [    1.291336] colorimetry 0->0
Jan 25 00:00:00 kernel: [    1.291338] hres 0->1920
Jan 25 00:00:00 kernel: [    1.291340] htotal 0->2200
Jan 25 00:00:00 kernel: [    1.291342] vres 0->1080
Jan 25 00:00:00 kernel: [    1.291343] vtotal 0->1125
Jan 25 00:00:00 kernel: [    1.291345] misc0_1 0x0->0x4021
Jan 25 00:00:00 kernel: [    1.291347] mvid 0x0->0x686a
Jan 25 00:00:00 kernel: [    1.291348] nvid 0x0->0x7e90
Jan 25 00:00:00 kernel: [    1.291350] vbid 0x0->0x11
Jan 25 00:00:00 kernel: [    1.291353] pixel clk 222750000
Jan 25 00:00:00 kernel: [    1.291356] color mode 28000
Jan 25 00:00:00 dhcpd: Listening on LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 dhcpd: Sending on   LPF/usb1/fc:d2:b6:ad:cc:6b/169.254.1.0/24
Jan 25 00:00:00 kernel: [    1.324688] Camera plug detect get_cam_plug_detect_config index = 0 
Jan 25 00:00:00 kernel: [    1.331275] get_ts_config index = 0 
Jan 25 00:00:00 kernel: [    1.332948] oled_open idx: 1
Jan 25 00:00:00 kernel: [    1.333052] oled_open idx: 0
Jan 25 00:00:00 kernel: [    1.369478] OLED:ecx343_left initialized successfully with config_index: 2
Jan 25 00:00:00 kernel: [    1.369486] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:00 kernel: [    1.370958] OLED:ecx343_right initialized successfully with config_index: 2
Jan 25 00:00:00 kernel: [    1.370960] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:00 kernel: [    1.371096] Set orbit_h: 0
Jan 25 00:00:00 kernel: [    1.371193] Set orbit_h: 0
Jan 25 00:00:00 kernel: [    1.371290] Set orbit_v: 0
Jan 25 00:00:00 kernel: [    1.371386] Set orbit_v: 0
Jan 25 00:00:00 kernel: [    1.371497] Set white coordinate x:-13, y:-18
Jan 25 00:00:00 kernel: [    1.371530] Set white coordinate x:-13, y:-18
Jan 25 00:00:00 dhcpd: Listening on LPF/usb0/fc:d2:b6:ad:cc:6a/***********/24
Jan 25 00:00:00 dhcpd: Sending on   LPF/usb0/fc:d2:b6:ad:cc:6a/***********/24
Jan 25 00:00:00 dhcpd: Sending on   Socket/fallback/fallback-net
Jan 25 00:00:00 dhcpd: Server starting service.
Jan 25 00:00:00 kernel: [    1.393912] ar_vb_open
Jan 25 00:00:00 kernel: [    1.394003] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:00 kernel: [    1.399960] get_ts_config index = 0 
Jan 25 00:00:00 kernel: [    1.399969] Enable timestamp irq
Jan 25 00:00:00 kernel: [    1.399983] Invalid ioctl cmd
Jan 25 00:00:01 kernel: [    1.461791] dwc2 8080000.usb: new address 1
Jan 25 00:00:01 kernel: [    1.466221] binder: 491:621 refcount change on invalid ref 0
Jan 25 00:00:01 kernel: [    1.736037] configfs-gadget gadget: high-speed config #1: b
Jan 25 00:00:01 kernel: [    1.759421] IPv6: ADDRCONF(NETDEV_CHANGE): usb0: link becomes ready
Jan 25 00:00:01 kernel: [    1.803483] IPv6: ADDRCONF(NETDEV_CHANGE): usb1: link becomes ready
Jan 25 00:00:01 kernel: [    1.816531] ar_vb_open
Jan 25 00:00:01 kernel: [    1.816654] hil_mmb_alloc pa:0x3bcd6000 len:32768!
Jan 25 00:00:01 kernel: [    1.826749] hil_mmb_alloc pa:0x3bcdf000 len:65536!
Jan 25 00:00:01 kernel: [    1.829449] ar_vb_open
Jan 25 00:00:01 kernel: [    1.832356] random: crng init done
Jan 25 00:00:01 kernel: [    1.832885] start success
Jan 25 00:00:01 kernel: [    1.838472] start success
Jan 25 00:00:01 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPREQUEST for ************ (***********) from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:01 kernel: [    1.882338] Open is called.
Jan 25 00:00:01 kernel: [    1.883184] Open is called.
Jan 25 00:00:01 kernel: [    1.887198] Open is called.
Jan 25 00:00:01 kernel: [    1.887236] dev_0 request  IRQ success
Jan 25 00:00:01 kernel: [    1.890764] dev_1 request  IRQ success
Jan 25 00:00:01 kernel: [    1.892107] Enabling LVDS lowest power...
Jan 25 00:00:01 kernel: [    1.892117] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:00:01 kernel: [    1.892119] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:00:01 kernel: [    1.892121] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:00:01 kernel: [    1.892124] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.892125] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:00:01 kernel: [    1.892127] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.892129] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:00:01 kernel: [    1.892131] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.892133] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:00:01 kernel: [    1.892135] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.892137] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:00:01 kernel: [    1.892139] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:00:01 kernel: [    1.892141] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:00:01 kernel: [    1.892143] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.892145] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:00:01 kernel: [    1.892147] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.892148] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:00:01 kernel: [    1.892150] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.892152] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:00:01 kernel: [    1.892154] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.892156] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:00:01 kernel: [    1.892158] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:00:01 kernel: [    1.892159] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:00:01 kernel: [    1.892161] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.892163] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:00:01 kernel: [    1.892165] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.892167] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:00:01 kernel: [    1.892169] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.892171] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:00:01 kernel: [    1.892173] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.892174] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:00:01 kernel: [    1.892176] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:00:01 kernel: [    1.892178] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:00:01 kernel: [    1.892180] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:00:01 kernel: [    1.892182] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:00:01 kernel: [    1.892184] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:00:01 kernel: [    1.892186] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:00:01 kernel: [    1.892187] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:00:01 kernel: [    1.892189] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:00:01 kernel: [    1.892191] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:00:01 kernel: [    1.892453] ar_vb_open
Jan 25 00:00:01 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPREQUEST for ************ (***********) from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:01 kernel: [    2.431097] inv_mpu: imu fifo count mismatch, fifo=1, irq_ts=0
Jan 25 00:00:02 kernel: [    2.594228] 6921d 2 3
Jan 25 00:00:02 kernel: [    2.877664] read descriptors
Jan 25 00:00:02 kernel: [    2.877678] read strings
Jan 25 00:00:02 kernel: [    2.885938] exFAT-fs (mmcblk0p22[259:14]): trying to mount...
Jan 25 00:00:02 kernel: [    2.886589] exFAT-fs (mmcblk0p22[259:14]): set logical sector size  : 512
Jan 25 00:00:02 kernel: [    2.886600] exFAT-fs (mmcblk0p22[259:14]): (bps : 512, spc : 64, data start : 3072, aligned)
Jan 25 00:00:02 kernel: [    2.886604] exFAT-fs (mmcblk0p22[259:14]): detected volume size     : 2097152 KB (disk : 3829760 KB, part : 2097152 KB)
Jan 25 00:00:02 kernel: [    2.894802] exFAT-fs (mmcblk0p22[259:14]): mounted successfully!
Jan 25 00:00:02 kernel: [    2.905949] Read iProduct:XREAL One
Jan 25 00:00:02 kernel: [    2.906759] Read iProduct:XREAL One
Jan 25 00:00:02 kernel: [    2.907121] artosyn_kuiper_usb2phy_init 105
Jan 25 00:00:02 kernel: [    2.907158] dwc2 8080000.usb: bound driver configfs-gadget
Jan 25 00:00:02 kernel: [    2.948739] 69000 1 b
Jan 25 00:00:02 kernel: [    3.049873] 6900b 2 10
Jan 25 00:00:02 kernel: [    3.050394] 6901b 2 10
Jan 25 00:00:02 kernel: [    3.050906] 6902b 2 10
Jan 25 00:00:02 kernel: [    3.051416] 6903b 2 10
Jan 25 00:00:02 kernel: [    3.051929] 6904b 2 10
Jan 25 00:00:02 kernel: [    3.052433] 6905b 2 10
Jan 25 00:00:02 kernel: [    3.052941] 6906b 2 10
Jan 25 00:00:02 kernel: [    3.053452] 6907b 2 10
Jan 25 00:00:02 kernel: [    3.053995] 6908b 2 10
Jan 25 00:00:02 kernel: [    3.054550] 6909b 2 10
Jan 25 00:00:02 kernel: [    3.055084] 690ab 2 10
Jan 25 00:00:02 kernel: [    3.055621] 690bb 2 10
Jan 25 00:00:02 kernel: [    3.056149] 690cb 2 10
Jan 25 00:00:02 kernel: [    3.056679] 690db 2 10
Jan 25 00:00:02 kernel: [    3.057214] 690eb 2 10
Jan 25 00:00:02 kernel: [    3.057796] 690fb 2 10
Jan 25 00:00:02 kernel: [    3.058430] 6910b 2 10
Jan 25 00:00:02 kernel: [    3.058975] 6911b 2 10
Jan 25 00:00:02 kernel: [    3.059512] 6912b 2 10
Jan 25 00:00:02 kernel: [    3.060054] 6913b 2 10
Jan 25 00:00:02 kernel: [    3.060603] 6914b 2 10
Jan 25 00:00:02 kernel: [    3.061146] 6915b 2 10
Jan 25 00:00:02 kernel: [    3.062114] 6916b 2 10
Jan 25 00:00:02 kernel: [    3.062649] 6917b 2 10
Jan 25 00:00:02 kernel: [    3.063206] 6918b 2 10
Jan 25 00:00:02 kernel: [    3.063742] 6919b 2 10
Jan 25 00:00:02 kernel: [    3.064301] 691ab 2 10
Jan 25 00:00:02 kernel: [    3.064835] 691bb 2 10
Jan 25 00:00:02 kernel: [    3.065387] 691cb 2 10
Jan 25 00:00:02 kernel: [    3.065980] 691db 2 10
Jan 25 00:00:02 kernel: [    3.066522] 691eb 2 10
Jan 25 00:00:02 kernel: [    3.067092] 691fb 2 10
Jan 25 00:00:02 kernel: [    3.067636] 6920b 2 10
Jan 25 00:00:02 kernel: [    3.068219] 6921b 2 5
Jan 25 00:00:02 kernel: [    3.111455] ------------[ cut here ]------------
Jan 25 00:00:02 kernel: [    3.111479] WARNING: CPU: 2 PID: 869 at ./include/linux/kref.h:46 kobject_get+0x64/0x88
Jan 25 00:00:02 kernel: [    3.111482] Modules linked in: aw883xx_drv(O) iqs323_drv(O) ar_pack(O) camera_plug_detect(O) timestamp_record_exp(O) camera_pwr_ioctl(O) ar_mpp_proc_ctrl(O) ar_sys(O) ar_vb(O) ar_osal(O) ar_mpp_drv(O)
Jan 25 00:00:02 kernel: [    3.111505] 
Jan 25 00:00:02 kernel: [    3.111512] CPU: 2 PID: 869 Comm: command_service Tainted: G           O    4.9.38 #3
Jan 25 00:00:02 kernel: [    3.111514] Hardware name: Artosyn, Kuiper Development Board (DT)
Jan 25 00:00:02 kernel: [    3.111517] task: ffffffc02e7e8b00 task.stack: ffffffc03012c000
Jan 25 00:00:02 kernel: [    3.111521] PC is at kobject_get+0x64/0x88
Jan 25 00:00:02 kernel: [    3.111526] LR is at cdev_get+0x30/0x68
Jan 25 00:00:02 kernel: [    3.111529] pc : [<ffffff800831c53c>] lr : [<ffffff800818e400>] pstate: 60000145
Jan 25 00:00:02 kernel: [    3.111530] sp : ffffffc03012fb00
Jan 25 00:00:02 kernel: [    3.111532] x29: ffffffc03012fb00 x28: ffffff800818def0 
Jan 25 00:00:02 kernel: [    3.111537] x27: 0000000000000000 x26: ffffffc030275a80 
Jan 25 00:00:02 kernel: [    3.111540] x25: 00000000000000ee x24: ffffffc03012fbfc 
Jan 25 00:00:02 kernel: [    3.111544] x23: 000000000ee00000 x22: ffffffc031009800 
Jan 25 00:00:02 kernel: [    3.111548] x21: 0000000000000000 x20: 0000000000000000 
Jan 25 00:00:02 kernel: [    3.111551] x19: ffffffc030275a80 x18: 0000007fd561774d 
Jan 25 00:00:02 kernel: [    3.111555] x17: 0000007f9c3d3c90 x16: ffffff80081883a0 
Jan 25 00:00:02 kernel: [    3.111559] x15: 000000000000000a x14: 0000000000000001 
Jan 25 00:00:02 kernel: [    3.111562] x13: 0000000000000000 x12: 0000000000000000 
Jan 25 00:00:02 kernel: [    3.111566] x11: 0000000000000000 x10: d0d0d0e0b7b4b9b8 
Jan 25 00:00:02 kernel: [    3.111569] x9 : 0000000000000000 x8 : ffffffc0313a6920 
Jan 25 00:00:02 kernel: [    3.111573] x7 : 0000000000000009 x6 : 0000000000000000 
Jan 25 00:00:02 kernel: [    3.111576] x5 : ffffffc0313a6900 x4 : 0000000000001000 
Jan 25 00:00:02 kernel: [    3.111580] x3 : 0000000000000000 x2 : ffffffc030275ab8 
Jan 25 00:00:02 kernel: [    3.111583] x1 : 0000000000000001 x0 : ffffff8008c27000 
Jan 25 00:00:02 kernel: [    3.111586] 
Jan 25 00:00:02 kernel: [    3.111588] ---[ end trace e0928a75a03c4c02 ]---
Jan 25 00:00:02 kernel: [    3.111591] Call trace:
Jan 25 00:00:02 kernel: [    3.111596] Exception stack(0xffffffc03012f930 to 0xffffffc03012fa60)
Jan 25 00:00:02 kernel: [    3.111599] f920:                                   ffffffc030275a80 0000008000000000
Jan 25 00:00:02 kernel: [    3.111603] f940: ffffffc03012fb00 ffffff800831c53c ffffffc03012f960 ffffff800820d764
Jan 25 00:00:02 kernel: [    3.111607] f960: ffffffc03012f990 ffffff8008162688 00000000000000e2 ffffffc03012fa48
Jan 25 00:00:02 kernel: [    3.111610] f980: ffffffc03012f990 ffffff800814d750 ffffffc03012f9a0 ffffff8008345114
Jan 25 00:00:02 kernel: [    3.111614] f9a0: ffffffc03012f9e0 ffffff80080adca4 0000000000000003 ffffffc03009e300
Jan 25 00:00:02 kernel: [    3.111617] f9c0: 0000000000000000 0000000000000001 ffffff8008c27000 0000000000000001
Jan 25 00:00:02 kernel: [    3.111621] f9e0: ffffffc030275ab8 0000000000000000 0000000000001000 ffffffc0313a6900
Jan 25 00:00:02 kernel: [    3.111624] fa00: 0000000000000000 0000000000000009 ffffffc0313a6920 0000000000000000
Jan 25 00:00:02 kernel: [    3.111628] fa20: d0d0d0e0b7b4b9b8 0000000000000000 0000000000000000 0000000000000000
Jan 25 00:00:02 kernel: [    3.111631] fa40: 0000000000000001 000000000000000a ffffff80081883a0 0000007f9c3d3c90
Jan 25 00:00:02 kernel: [    3.111636] [<ffffff800831c53c>] kobject_get+0x64/0x88
Jan 25 00:00:02 kernel: [    3.111639] [<ffffff800818e400>] cdev_get+0x30/0x68
Jan 25 00:00:02 kernel: [    3.111642] [<ffffff800818e448>] exact_lock+0x10/0x20
Jan 25 00:00:02 kernel: [    3.111652] [<ffffff80083b2d40>] kobj_lookup+0xc8/0x158
Jan 25 00:00:02 kernel: [    3.111656] [<ffffff800818e830>] chrdev_open+0x110/0x1a0
Jan 25 00:00:02 kernel: [    3.111663] [<ffffff8008186e30>] do_dentry_open.isra.1+0x178/0x318
Jan 25 00:00:02 kernel: [    3.111668] [<ffffff8008187e7c>] vfs_open+0x44/0x70
Jan 25 00:00:02 kernel: [    3.111675] [<ffffff80081984fc>] path_openat+0x4fc/0x1030
Jan 25 00:00:02 kernel: [    3.111679] [<ffffff8008199fe8>] do_filp_open+0x60/0xd8
Jan 25 00:00:02 kernel: [    3.111682] [<ffffff80081882d8>] do_sys_open+0x170/0x210
Jan 25 00:00:02 kernel: [    3.111686] [<ffffff80081883b0>] SyS_openat+0x10/0x18
Jan 25 00:00:02 kernel: [    3.111692] [<ffffff80080a2730>] el0_svc_naked+0x24/0x28
Jan 25 00:00:02 kernel: [    3.125107] 692a0 1 10
Jan 25 00:00:02 kernel: [    3.125469] 692b0 1 10
Jan 25 00:00:02 kernel: [    3.125491] artosyn_dprx_irq_thread_1 692b0
Jan 25 00:00:02 kernel: [    3.125495] artosyn_dprx_hdcp_2_2_calculation 692b0
Jan 25 00:00:02 kernel: [    3.128977] store km: e3 46 66 2a
Jan 25 00:00:02 kernel: [    3.128985] store km: 40 08 47 10
Jan 25 00:00:02 kernel: [    3.128996] store km: 8f b9 88 91
Jan 25 00:00:02 kernel: [    3.128998] store km: 66 f8 b8 cf
Jan 25 00:00:02 kernel: [    3.129007] cal done 10000
Jan 25 00:00:02 kernel: [    3.129012] cal done 10000
Jan 25 00:00:02 kernel: [    3.129015] hw kd: 4c 08 fa 04
Jan 25 00:00:02 kernel: [    3.129017] hw kd: eb a2 bc da
Jan 25 00:00:02 kernel: [    3.129019] hw kd: 2f 66 4b 2c
Jan 25 00:00:02 kernel: [    3.129020] hw kd: e2 e6 eb d5
Jan 25 00:00:02 kernel: [    3.129022] hw kd: 3d 9b 7b 2a
Jan 25 00:00:02 kernel: [    3.129026] hw kd: 78 66 fe 94
Jan 25 00:00:02 kernel: [    3.129028] hw kd: 0d e8 9c d5
Jan 25 00:00:02 kernel: [    3.129030] hw kd: 10 73 65 74
Jan 25 00:00:02 kernel: [    3.131328] h: d9 49 c6 ea
Jan 25 00:00:02 kernel: [    3.131336] h: 97 9c fa 8d
Jan 25 00:00:02 kernel: [    3.131338] h: 8e 92 48 b3
Jan 25 00:00:02 kernel: [    3.131340] h: 98 d9 07 50
Jan 25 00:00:02 kernel: [    3.131342] h: 37 67 0f 9c
Jan 25 00:00:02 kernel: [    3.131344] h: 93 b1 9f d2
Jan 25 00:00:02 kernel: [    3.131346] h: 7a 4f d7 c6
Jan 25 00:00:02 kernel: [    3.131348] h: 68 52 4f 4b
Jan 25 00:00:02 kernel: [    3.131359] artosyn_typec_event 1631 1!
Jan 25 00:00:02 kernel: [    3.131370] dp_sink_altmode_event 485 1 dp->hpd:1 hpd:1 irq:256
Jan 25 00:00:02 kernel: [    3.131379] dp_sink_altmode_attention_vdm 300
Jan 25 00:00:02 kernel: [    3.131435] artosyn_dprx_dp_pd_event_work 2879 1
Jan 25 00:00:02 kernel: [    3.137809] 69493 2 1
Jan 25 00:00:02 kernel: [    3.137819] rx_status 2
Jan 25 00:00:02 kernel: [    3.138257] 692c0 2 10
Jan 25 00:00:02 kernel: [    3.138786] 692d0 2 10
Jan 25 00:00:02 kernel: [    3.142934] 692f0 1 8
Jan 25 00:00:02 kernel: [    3.142943] L' start
Jan 25 00:00:02 kernel: [    3.142958] artosyn_dprx_irq_thread_1 692f0
Jan 25 00:00:02 kernel: [    3.142962] artosyn_dprx_hdcp_2_2_calculation 692f0
Jan 25 00:00:02 kernel: [    3.143038] L' end
Jan 25 00:00:02 kernel: [    3.143423] 692f8 2 10
Jan 25 00:00:02 kernel: [    3.143951] 69308 2 10
Jan 25 00:00:02 kernel: [    3.149962] 69318 1 10
Jan 25 00:00:02 kernel: [    3.150254] 69328 1 8
Jan 25 00:00:02 kernel: [    3.150269] artosyn_dprx_irq_thread_1 69328
Jan 25 00:00:02 kernel: [    3.150272] artosyn_dprx_hdcp_2_2_calculation 69328
Jan 25 00:00:02 kernel: [    3.150279] cal done 10000
Jan 25 00:00:02 kernel: [    3.150282] hw kd2: f2 60 5e 43
Jan 25 00:00:02 kernel: [    3.150284] hw kd2: f6 64 0f 66
Jan 25 00:00:02 kernel: [    3.150286] hw kd2: 24 5e 7a 02
Jan 25 00:00:02 kernel: [    3.150288] hw kd2: ff 5d 56 54
Jan 25 00:00:02 kernel: [    3.150291] r_n: 28 b9 c6 04
Jan 25 00:00:02 kernel: [    3.150293] r_n: 6f 5b 41 9b
Jan 25 00:00:02 kernel: [    3.150294] tx_r: c2 49 80 18
Jan 25 00:00:02 kernel: [    3.150296] tx_r: 29 c0 ed 34
Jan 25 00:00:02 kernel: [    3.150299] rx_r: 0c 24 55 cd
Jan 25 00:00:02 kernel: [    3.150301] rx_r: 3d 41 12 fb
Jan 25 00:00:02 kernel: [    3.150303] e_dkey_ks: 57 60 84 f2
Jan 25 00:00:02 kernel: [    3.150304] e_dkey_ks: 6f 58 a4 fd
Jan 25 00:00:02 kernel: [    3.150307] e_dkey_ks: 20 d8 89 a0
Jan 25 00:00:02 kernel: [    3.150308] e_dkey_ks: 64 55 2f 64
Jan 25 00:00:02 kernel: [    3.150311] ks: a5 00 da b1
Jan 25 00:00:02 kernel: [    3.150312] ks: 99 3c ab 9b
Jan 25 00:00:02 kernel: [    3.150314] ks: 08 a2 a6 6f
Jan 25 00:00:02 kernel: [    3.150316] ks: a6 49 6b cb
Jan 25 00:00:02 kernel: [    3.150323] lc: b5 d8 e9 ab
Jan 25 00:00:02 kernel: [    3.150325] lc: 5f 8a fe ca
Jan 25 00:00:02 kernel: [    3.150327] lc: 38 55 b1 a5
Jan 25 00:00:02 kernel: [    3.150329] lc: 1e c9 bc 0f
Jan 25 00:00:02 kernel: [    3.150331] r_iv: e2 13 58 75
Jan 25 00:00:02 kernel: [    3.150333] r_iv: 5c 5f f8 d1
Jan 25 00:00:02 kernel: [    3.150334] artosyn_dprx_hdcp_2_2_start
Jan 25 00:00:02 kernel: [    3.150559] 69494 1 1
Jan 25 00:00:02 kernel: [    3.150562] type 0
Jan 25 00:00:02 kernel: [    3.168048] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:02 kernel: [    3.288074] dwc2 8080000.usb: new device is high-speed
Jan 25 00:00:02 kernel: [    3.361717] dwc2 8080000.usb: new address 2
Jan 25 00:00:03 kernel: [    3.655962] configfs-gadget gadget: high-speed config #1: b
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ************
Jan 25 00:00:03 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ************
Jan 25 00:00:03 dhcpd: DHCPREQUEST for ************ (***********) from fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6c via usb1
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ************
Jan 25 00:00:03 dhcpd: DHCPDISCOVER from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:03 dhcpd: DHCPOFFER on ************ to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:03 dhcpd: reuse_lease: lease age 2 (secs) under 25% threshold, reply with unaltered, existing lease for ************
Jan 25 00:00:03 dhcpd: DHCPREQUEST for ************ (***********) from fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:03 dhcpd: DHCPACK on ************ to fc:d2:b6:ad:cc:6d via usb0
Jan 25 00:00:04 kernel: [    5.347549] dev_0 SUB_CLEAR success
Jan 25 00:00:04 kernel: [    5.351119] dev_0 free IRQ success
Jan 25 00:00:04 kernel: [    5.352106] dev_1 SUB_CLEAR success
Jan 25 00:00:04 kernel: [    5.352321] dev_1 free IRQ success
Jan 25 00:00:04 kernel: [    5.370454] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370462] mmb(0x3BD38000) not found!
Jan 25 00:00:04 kernel: [    5.370576] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370578] mmb(0x3BD39000) not found!
Jan 25 00:00:04 kernel: [    5.370626] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370628] mmb(0x3BD3A000) not found!
Jan 25 00:00:04 kernel: [    5.370674] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370676] mmb(0x3BD3B000) not found!
Jan 25 00:00:04 kernel: [    5.370716] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370718] mmb(0x3BD3C000) not found!
Jan 25 00:00:04 kernel: [    5.370760] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370762] mmb(0x3BD3D000) not found!
Jan 25 00:00:04 kernel: [    5.370800] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370802] mmb(0x3BD3E000) not found!
Jan 25 00:00:04 kernel: [    5.370842] mmz_userdev:get_mmbinfo_safe: 
Jan 25 00:00:04 kernel: [    5.370843] mmb(0x3BD3F000) not found!
Jan 25 00:00:04 kernel: [    5.375451] OLED:ecx343_left initialized successfully with config_index: 2
Jan 25 00:00:04 kernel: [    5.375458] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:04 kernel: [    5.376801] OLED:ecx343_right initialized successfully with config_index: 2
Jan 25 00:00:04 kernel: [    5.376803] OLED initialized successfully with initial_code_idx: 2
Jan 25 00:00:04 kernel: [    5.376987] Set orbit_h: 0
Jan 25 00:00:04 kernel: [    5.377075] Set orbit_h: 0
Jan 25 00:00:04 kernel: [    5.377161] Set orbit_v: 0
Jan 25 00:00:04 kernel: [    5.377245] Set orbit_v: 0
Jan 25 00:00:05 kernel: [    5.581156] dev_0 request  IRQ success
Jan 25 00:00:05 kernel: [    5.584621] dev_1 request  IRQ success
Jan 25 00:00:05 kernel: [    5.585832] Enabling LVDS lowest power...
Jan 25 00:00:05 kernel: [    5.585841] Write Value: 0x80 to Address: 0xffffff8008edd018
Jan 25 00:00:05 kernel: [    5.585850] Verification successful at Address: 0xffffff8008edd018. Value: 0x80
Jan 25 00:00:05 kernel: [    5.585852] Write Value: 0x1a to Address: 0xffffff8008edd390
Jan 25 00:00:05 kernel: [    5.585854] Verification successful at Address: 0xffffff8008edd390. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.585855] Write Value: 0x1 to Address: 0xffffff8008edd0c0
Jan 25 00:00:05 kernel: [    5.585857] Verification successful at Address: 0xffffff8008edd0c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.585858] Write Value: 0xc0 to Address: 0xffffff8008edd39c
Jan 25 00:00:05 kernel: [    5.585860] Verification successful at Address: 0xffffff8008edd39c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.585862] Write Value: 0xad to Address: 0xffffff8008edd3a0
Jan 25 00:00:05 kernel: [    5.585864] Verification successful at Address: 0xffffff8008edd3a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.585866] Write Value: 0x80 to Address: 0xffffff8008f03418
Jan 25 00:00:05 kernel: [    5.585867] Verification successful at Address: 0xffffff8008f03418. Value: 0x80
Jan 25 00:00:05 kernel: [    5.585869] Write Value: 0x1a to Address: 0xffffff8008f03790
Jan 25 00:00:05 kernel: [    5.585870] Verification successful at Address: 0xffffff8008f03790. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.585872] Write Value: 0x1 to Address: 0xffffff8008f034c0
Jan 25 00:00:05 kernel: [    5.585873] Verification successful at Address: 0xffffff8008f034c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.585875] Write Value: 0xc0 to Address: 0xffffff8008f0379c
Jan 25 00:00:05 kernel: [    5.585877] Verification successful at Address: 0xffffff8008f0379c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.585878] Write Value: 0xad to Address: 0xffffff8008f037a0
Jan 25 00:00:05 kernel: [    5.585880] Verification successful at Address: 0xffffff8008f037a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.585881] Write Value: 0x80 to Address: 0xffffff8008f05018
Jan 25 00:00:05 kernel: [    5.585883] Verification successful at Address: 0xffffff8008f05018. Value: 0x80
Jan 25 00:00:05 kernel: [    5.585885] Write Value: 0x1a to Address: 0xffffff8008f05390
Jan 25 00:00:05 kernel: [    5.585886] Verification successful at Address: 0xffffff8008f05390. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.585888] Write Value: 0x1 to Address: 0xffffff8008f050c0
Jan 25 00:00:05 kernel: [    5.585889] Verification successful at Address: 0xffffff8008f050c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.585891] Write Value: 0xc0 to Address: 0xffffff8008f0539c
Jan 25 00:00:05 kernel: [    5.585893] Verification successful at Address: 0xffffff8008f0539c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.585894] Write Value: 0xad to Address: 0xffffff8008f053a0
Jan 25 00:00:05 kernel: [    5.585896] Verification successful at Address: 0xffffff8008f053a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.585897] Write Value: 0x80 to Address: 0xffffff8008f07418
Jan 25 00:00:05 kernel: [    5.585899] Verification successful at Address: 0xffffff8008f07418. Value: 0x80
Jan 25 00:00:05 kernel: [    5.585901] Write Value: 0x1a to Address: 0xffffff8008f07790
Jan 25 00:00:05 kernel: [    5.585902] Verification successful at Address: 0xffffff8008f07790. Value: 0x1a
Jan 25 00:00:05 kernel: [    5.585904] Write Value: 0x1 to Address: 0xffffff8008f074c0
Jan 25 00:00:05 kernel: [    5.585906] Verification successful at Address: 0xffffff8008f074c0. Value: 0x1
Jan 25 00:00:05 kernel: [    5.585907] Write Value: 0xc0 to Address: 0xffffff8008f0779c
Jan 25 00:00:05 kernel: [    5.585909] Verification successful at Address: 0xffffff8008f0779c. Value: 0xc0
Jan 25 00:00:05 kernel: [    5.585910] Write Value: 0xad to Address: 0xffffff8008f077a0
Jan 25 00:00:05 kernel: [    5.585912] Verification successful at Address: 0xffffff8008f077a0. Value: 0xad
Jan 25 00:00:05 kernel: [    5.588669] Get switch state: 0
Jan 25 00:00:05 kernel: [    5.588740] Get switch state: 0
Jan 25 00:00:05 kernel: [    5.613168] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.613175] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.613250] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.613251] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.613597] Get switch state: 1
Jan 25 00:00:05 kernel: [    5.613675] Get switch state: 1
Jan 25 00:00:05 kernel: [    5.614929] Set white coordinate x:-17, y:-22
Jan 25 00:00:05 kernel: [    5.615569] Set white coordinate x:-21, y:-23
Jan 25 00:00:05 kernel: [    5.639929] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.639938] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.640017] Brightness register values: 00 00
Jan 25 00:00:05 kernel: [    5.640018] Get brightness: 0
Jan 25 00:00:05 kernel: [    5.679394] Brightness register values: 00 2c
Jan 25 00:00:05 kernel: [    5.679406] Get brightness: 44
Jan 25 00:00:05 kernel: [    5.679499] Brightness register values: 00 2c
Jan 25 00:00:05 kernel: [    5.679501] Get brightness: 44
Jan 25 00:00:05 kernel: [    5.719446] Brightness register values: 00 58
Jan 25 00:00:05 kernel: [    5.719461] Get brightness: 88
Jan 25 00:00:05 kernel: [    5.719541] Brightness register values: 00 58
Jan 25 00:00:05 kernel: [    5.719543] Get brightness: 88
Jan 25 00:00:05 kernel: [    5.759366] Brightness register values: 00 84
Jan 25 00:00:05 kernel: [    5.759376] Get brightness: 132
Jan 25 00:00:05 kernel: [    5.759452] Brightness register values: 00 84
Jan 25 00:00:05 kernel: [    5.759453] Get brightness: 132
Jan 25 00:00:05 kernel: [    5.799310] Brightness register values: 00 b0
Jan 25 00:00:05 kernel: [    5.799320] Get brightness: 176
Jan 25 00:00:05 kernel: [    5.799395] Brightness register values: 00 b0
Jan 25 00:00:05 kernel: [    5.799397] Get brightness: 176
Jan 25 00:00:05 kernel: [    5.839586] Brightness register values: 00 dc
Jan 25 00:00:05 kernel: [    5.839599] Get brightness: 220
Jan 25 00:00:05 kernel: [    5.839677] Brightness register values: 00 dc
Jan 25 00:00:05 kernel: [    5.839679] Get brightness: 220
Jan 25 00:00:05 kernel: [    5.879303] Brightness register values: 01 08
Jan 25 00:00:05 kernel: [    5.879314] Get brightness: 264
Jan 25 00:00:05 kernel: [    5.879390] Brightness register values: 01 08
Jan 25 00:00:05 kernel: [    5.879392] Get brightness: 264
Jan 25 00:00:05 kernel: [    5.919321] Brightness register values: 01 34
Jan 25 00:00:05 kernel: [    5.919331] Get brightness: 308
Jan 25 00:00:05 kernel: [    5.919407] Brightness register values: 01 34
Jan 25 00:00:05 kernel: [    5.919409] Get brightness: 308
Jan 25 00:00:05 kernel: [    5.959284] Brightness register values: 01 60
Jan 25 00:00:05 kernel: [    5.959295] Get brightness: 352
Jan 25 00:00:05 kernel: [    5.959370] Brightness register values: 01 60
Jan 25 00:00:05 kernel: [    5.959371] Get brightness: 352
Jan 25 00:00:05 kernel: [    5.999295] Brightness register values: 01 8c
Jan 25 00:00:05 kernel: [    5.999305] Get brightness: 396
Jan 25 00:00:05 kernel: [    5.999379] Brightness register values: 01 8c
Jan 25 00:00:05 kernel: [    5.999380] Get brightness: 396
