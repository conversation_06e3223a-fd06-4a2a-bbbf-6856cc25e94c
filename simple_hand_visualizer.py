#!/usr/bin/env python3
"""
Simple Hand Tracking Visualizer

A simplified script to quickly visualize hand movement from hand_in_world.log
"""

import re
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
from datetime import datetime

def parse_log_simple(filename):
    """Parse log file and return hand tracking data"""
    hand0_data = {'time': [], 'x': [], 'y': [], 'z': []}
    hand1_data = {'time': [], 'x': [], 'y': [], 'z': []}
    
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*hand_data(\d+) in world x:([-\d\.]+) y:([-\d\.]+) z:([-\d\.]+)'
    
    with open(filename, 'r') as file:
        for line in file:
            match = re.search(pattern, line)
            if match:
                timestamp_str = match.group(1)
                hand_id = int(match.group(2))
                x = float(match.group(3))
                y = float(match.group(4))
                z = float(match.group(5))
                
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                
                if hand_id == 0:
                    hand0_data['time'].append(timestamp)
                    hand0_data['x'].append(x)
                    hand0_data['y'].append(y)
                    hand0_data['z'].append(z)
                elif hand_id == 1:
                    hand1_data['time'].append(timestamp)
                    hand1_data['x'].append(x)
                    hand1_data['y'].append(y)
                    hand1_data['z'].append(z)
    
    return hand0_data, hand1_data

def plot_hand_tracking(hand0_data, hand1_data):
    """Create comprehensive visualization of hand tracking data"""
    
    fig = plt.figure(figsize=(16, 12))
    
    # 3D trajectory plot
    ax1 = fig.add_subplot(2, 2, 1, projection='3d')
    
    if hand0_data['x']:
        ax1.plot(hand0_data['x'], hand0_data['y'], hand0_data['z'], 
                'b-', label='Hand 0', linewidth=2, alpha=0.7)
        ax1.scatter(hand0_data['x'][0], hand0_data['y'][0], hand0_data['z'][0], 
                   c='blue', s=100, marker='o', label='Hand 0 Start')
        ax1.scatter(hand0_data['x'][-1], hand0_data['y'][-1], hand0_data['z'][-1], 
                   c='blue', s=100, marker='s', label='Hand 0 End')
    
    if hand1_data['x']:
        ax1.plot(hand1_data['x'], hand1_data['y'], hand1_data['z'], 
                'r-', label='Hand 1', linewidth=2, alpha=0.7)
        ax1.scatter(hand1_data['x'][0], hand1_data['y'][0], hand1_data['z'][0], 
                   c='red', s=100, marker='o', label='Hand 1 Start')
        ax1.scatter(hand1_data['x'][-1], hand1_data['y'][-1], hand1_data['z'][-1], 
                   c='red', s=100, marker='s', label='Hand 1 End')
    
    ax1.set_xlabel('X Position')
    ax1.set_ylabel('Y Position')
    ax1.set_zlabel('Z Position')
    ax1.set_title('3D Hand Trajectories')
    ax1.legend()
    
    # X, Y, Z positions over time
    ax2 = fig.add_subplot(2, 2, 2)
    
    if hand0_data['x']:
        ax2.plot(hand0_data['time'], hand0_data['x'], 'b-', label='Hand 0 X', alpha=0.7)
        ax2.plot(hand0_data['time'], hand0_data['y'], 'b--', label='Hand 0 Y', alpha=0.7)
        ax2.plot(hand0_data['time'], hand0_data['z'], 'b:', label='Hand 0 Z', alpha=0.7)
    
    if hand1_data['x']:
        ax2.plot(hand1_data['time'], hand1_data['x'], 'r-', label='Hand 1 X', alpha=0.7)
        ax2.plot(hand1_data['time'], hand1_data['y'], 'r--', label='Hand 1 Y', alpha=0.7)
        ax2.plot(hand1_data['time'], hand1_data['z'], 'r:', label='Hand 1 Z', alpha=0.7)
    
    ax2.set_xlabel('Time')
    ax2.set_ylabel('Position')
    ax2.set_title('Position Components Over Time')
    ax2.legend()
    ax2.tick_params(axis='x', rotation=45)
    
    # XY plane view
    ax3 = fig.add_subplot(2, 2, 3)
    
    if hand0_data['x']:
        ax3.plot(hand0_data['x'], hand0_data['y'], 'b-', label='Hand 0', linewidth=2, alpha=0.7)
        ax3.scatter(hand0_data['x'][0], hand0_data['y'][0], c='blue', s=100, marker='o')
        ax3.scatter(hand0_data['x'][-1], hand0_data['y'][-1], c='blue', s=100, marker='s')
    
    if hand1_data['x']:
        ax3.plot(hand1_data['x'], hand1_data['y'], 'r-', label='Hand 1', linewidth=2, alpha=0.7)
        ax3.scatter(hand1_data['x'][0], hand1_data['y'][0], c='red', s=100, marker='o')
        ax3.scatter(hand1_data['x'][-1], hand1_data['y'][-1], c='red', s=100, marker='s')
    
    ax3.set_xlabel('X Position')
    ax3.set_ylabel('Y Position')
    ax3.set_title('Movement in XY Plane')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Distance from origin over time
    ax4 = fig.add_subplot(2, 2, 4)
    
    if hand0_data['x']:
        distances0 = [np.sqrt(x**2 + y**2 + z**2) for x, y, z in 
                     zip(hand0_data['x'], hand0_data['y'], hand0_data['z'])]
        ax4.plot(hand0_data['time'], distances0, 'b-', label='Hand 0', linewidth=2, alpha=0.7)
    
    if hand1_data['x']:
        distances1 = [np.sqrt(x**2 + y**2 + z**2) for x, y, z in 
                     zip(hand1_data['x'], hand1_data['y'], hand1_data['z'])]
        ax4.plot(hand1_data['time'], distances1, 'r-', label='Hand 1', linewidth=2, alpha=0.7)
    
    ax4.set_xlabel('Time')
    ax4.set_ylabel('Distance from Origin')
    ax4.set_title('Distance from Origin Over Time')
    ax4.legend()
    ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('hand_movement_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_summary(hand0_data, hand1_data):
    """Print summary statistics"""
    print("=" * 50)
    print("HAND TRACKING SUMMARY")
    print("=" * 50)
    
    for hand_id, data in [(0, hand0_data), (1, hand1_data)]:
        if not data['x']:
            print(f"\nHand {hand_id}: No data found")
            continue
            
        print(f"\nHand {hand_id}:")
        print(f"  Data points: {len(data['x'])}")
        print(f"  Time range: {data['time'][0]} to {data['time'][-1]}")
        
        x_range = max(data['x']) - min(data['x'])
        y_range = max(data['y']) - min(data['y'])
        z_range = max(data['z']) - min(data['z'])
        
        print(f"  X range: {min(data['x']):.3f} to {max(data['x']):.3f} (span: {x_range:.3f})")
        print(f"  Y range: {min(data['y']):.3f} to {max(data['y']):.3f} (span: {y_range:.3f})")
        print(f"  Z range: {min(data['z']):.3f} to {max(data['z']):.3f} (span: {z_range:.3f})")
        
        # Calculate total movement
        total_distance = 0
        for i in range(1, len(data['x'])):
            dx = data['x'][i] - data['x'][i-1]
            dy = data['y'][i] - data['y'][i-1]
            dz = data['z'][i] - data['z'][i-1]
            total_distance += np.sqrt(dx**2 + dy**2 + dz**2)
        
        print(f"  Total distance traveled: {total_distance:.3f}")

def main():
    """Main function"""
    try:
        print("Analyzing hand_in_world.log...")
        hand0_data, hand1_data = parse_log_simple('hand_in_world.log')
        
        print_summary(hand0_data, hand1_data)
        
        print("\nGenerating visualizations...")
        plot_hand_tracking(hand0_data, hand1_data)
        
        print("Analysis complete! Check 'hand_movement_analysis.png' for the visualization.")
        
    except FileNotFoundError:
        print("Error: hand_in_world.log file not found!")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
